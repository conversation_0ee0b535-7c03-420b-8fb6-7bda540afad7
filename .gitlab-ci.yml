variables:
  APP_NAME: project-services-api
  APP_DLL_PATH: DWH.ProjectServices.API.dll
  TEST_PROJECT_NAME: DWH.ProjectServices.API.UnitTests
  DOJO_ENGAGEMENT_NAME: project-services-api
  SQ_PRODUCT_NAME: DWH.ProjectServices.API
  SQ_PROJECT_KEY: DWH.ProjectServices.API
  TEST_SETTINGS_FILE: cobertura.runsettings

include:
  - project: 'dp/de/shared/pipeline/generic-pipelines'
    file: 'dotnet/dotnet-8.yml'
