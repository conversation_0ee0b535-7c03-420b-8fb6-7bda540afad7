---
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: ProjectServicesAPI
  annotations:
    # grafana/dashboard-selector: "(tags @> 'kubernetes-mixin')"
    gitlab.com/project-id: '520'
    gitlab.com/project-slug: dp/de/products/builder/projectservices-api
    sonarqube.org/project-key: 'DWH.ProjectServices.API'
  description: 'ProjectServicesAPI'
  tags:
    - k8s
    - net
spec:
  lifecycle: production
  owner: titans
  type: service
  system: startrack
