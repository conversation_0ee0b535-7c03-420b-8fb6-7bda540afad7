﻿using DWH.ProjectServices.API.Infrastructure.WebServiceClient.Interfaces;
using DWH.ProjectServices.API.Infrastructure.WebServiceClient;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;

namespace DWH.ProjectServices.API.IntegrationTests.MockService
{
    public class APIFactory : WebApplicationFactory<Program>
    {
        public DateAPIServer DateAPIServer { get; }

        public JiraAPIServer JiraAPIServer { get; }
        public RoleAPIServer RoleAPIServer { get; }
        public HttpClient Client { get; private set; }

        public APIFactory()
        {
            DateAPIServer = new DateAPIServer();
            JiraAPIServer = new JiraAPIServer();
            RoleAPIServer = new RoleAPIServer();

            Client = this.WithWebHostBuilder(builder =>
            {
                builder.ConfigureServices(services =>
                {
                    services.AddHttpClient<IDateApiClient, DateApiClient>((serviceProvider, client) =>
                    {
                        var wireMockUrl = DateAPIServer.Server.Urls[0];
                        client.BaseAddress = new Uri(wireMockUrl);

                        // Override WebServiceClientOptions to ensure base address points to WireMock server
                        var options = serviceProvider.GetRequiredService<IOptions<WebServiceClientOptions>>().Value;
                        options.BaseAddress.DateAPI = wireMockUrl;
                    });
                    services.AddHttpClient<IJiraApiClient, JiraAPIClient>((serviceProvider, client) =>
                    {
                        var wireMockUrl = JiraAPIServer.Server.Urls[0];
                        client.BaseAddress = new Uri(wireMockUrl);

                        // Override WebServiceClientOptions to ensure base address points to WireMock server
                        var options = serviceProvider.GetRequiredService<IOptions<WebServiceClientOptions>>().Value;
                        options.BaseAddress.JiraAPI = wireMockUrl;
                    });
                    services.AddHttpClient<IRoleApiClient, RoleApiClient>((serviceProvider, client) =>
                    {
                        var wireMockUrl = RoleAPIServer.Server.Urls[0];
                        client.BaseAddress = new Uri(wireMockUrl);

                        // Override WebServiceClientOptions to ensure base address points to WireMock server
                        var options = serviceProvider.GetRequiredService<IOptions<WebServiceClientOptions>>().Value;
                        options.BaseAddress.RoleAPI = wireMockUrl;
                    });
                });
            }).CreateClient();
        }

        public new void Dispose()
        {
            DateAPIServer.Dispose();
            JiraAPIServer.Dispose();
            base.Dispose();
        }
    }
}
