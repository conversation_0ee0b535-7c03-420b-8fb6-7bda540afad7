﻿using WireMock.Server;
using WireMock.Settings;

public class WireMockManager : IDisposable
{
    public WireMockServer Server { get; private set; }

    public WireMockManager()
    {
        Server = WireMockServer.Start();

        // Stub for successful response
        Server
            .Given(WireMock.RequestBuilders.Request.Create()
                .WithPath("/api/v1/Periods/current")
                .WithParam("periodicityId", "1")
                .UsingGet())
            .RespondWith(WireMock.ResponseBuilders.Response.Create()
                .WithStatusCode(200)
                .WithBody(@"[
                {
                    ""Id"": ""1"",
                    ""Name"": ""Period 1""
                },
                {
                    ""Id"": ""2"",
                    ""Name"": ""Period 2""
                }
            ]"));

        // Stub for successful response
        Server
            .Given(WireMock.RequestBuilders.Request.Create()
                .WithPath("/api/v1/Periods/current")
                .WithParam("periodicityId", "4")
                .UsingGet())
            .RespondWith(WireMock.ResponseBuilders.Response.Create()
                .WithStatusCode(200)
                .WithBody(@"[
                {
                    ""Id"": ""1"",
                    ""Name"": ""Period 1""
                },
                {
                    ""Id"": ""2"",
                    ""Name"": ""Period 2""
                },
                {
                    ""Id"": ""3"",
                    ""Name"": ""Period 3""
                },
                {
                    ""Id"": ""4"",
                    ""Name"": ""Period 4""
                },
                {
                    ""Id"": ""5"",
                    ""Name"": ""Period 5""
                },
                {
                    ""Id"": ""6"",
                    ""Name"": ""Period 6""
                },
                {
                    ""Id"": ""7"",
                    ""Name"": ""Period 7""
                },
                {
                    ""Id"": ""8"",
                    ""Name"": ""Period 8""
                },
                {
                    ""Id"": ""9"",
                    ""Name"": ""Period 9""
                },
                {
                    ""Id"": ""10"",
                    ""Name"": ""Period 10""
                },
                {
                    ""Id"": ""11"",
                    ""Name"": ""Period 11""
                },
                {
                    ""Id"": ""12"",
                    ""Name"": ""Period 12""
                }
            ]"));

        // Stub for argument out of range exception
        Server
        .Given(WireMock.RequestBuilders.Request.Create()
            .WithPath("/api/v1/Periods/current")
            .WithParam("periodicityId", "999")
            .UsingGet())
        .RespondWith(WireMock.ResponseBuilders.Response.Create()
            .WithStatusCode(200)
            .WithBody("[]")
            .WithHeader("Content-Type", "application/json"));

        // Stub for external API failure
        Server
            .Given(WireMock.RequestBuilders.Request.Create()
                .WithPath("/api/v1/Periods/current")
                .WithParam("periodicityId", "500")
                .UsingGet())
            .RespondWith(WireMock.ResponseBuilders.Response.Create()
                .WithStatusCode(500)
                .WithBody("Internal Server Error"));


        // Stub for /api/v1/Periods/{id}/shifted endpoint with id = 20240699999030 and distances = -1
        Server
            .Given(WireMock.RequestBuilders.Request.Create()
                .WithPath("/api/v1/Periods/{id}/shifted")
                .WithParam("distances", "-1")
                .UsingGet())
            .RespondWith(WireMock.ResponseBuilders.Response.Create()
                .WithStatusCode(200)
                .WithBody(@"[
                    {
                        ""id"": 20240599999030,
                        ""name"": ""May 2024"",
                        ""shortName"": ""May24""
                    }
                ]"));

        // Stub for /api/v1/Periods/{id}/distances endpoint with id = 20240699999030 and offsetPeriods = 20240599999030
        Server
            .Given(WireMock.RequestBuilders.Request.Create()
                .WithPath("/api/v1/Periods/{id}/distances")
                .WithParam("offsetPeriods", "20240599999030")
                .UsingGet())
            .RespondWith(WireMock.ResponseBuilders.Response.Create()
                .WithStatusCode(200)
                .WithBody(@"[
                    {
                        ""fromPeriod"": 20240699999030,
                        ""toPeriod"": 20240599999030,
                        ""distance"": -1
                    }
                ]"));


        // Stub for /api/v1/Periods/{id}/shifted endpoint with id = 20240699999030 and distances = -1
        Server
            .Given(WireMock.RequestBuilders.Request.Create()
                .WithPath("/api/v1/Periods/{id}/shifted")
                .WithParam("distances", "-1")
                .UsingGet())
            .RespondWith(WireMock.ResponseBuilders.Response.Create()
                .WithStatusCode(200)
                .WithBody(@"[
                    {
                        ""id"": 20240599999030,
                        ""name"": ""May 2024"",
                        ""shortName"": ""May24""
                    }
                ]"));

        // Stub for /api/v1/Periods/{id}/distances endpoint with id = 20240699999030 and offsetPeriods = 20240599999030
        Server
            .Given(WireMock.RequestBuilders.Request.Create()
                .WithPath("/api/v1/Periods/{id}/distances")
                .WithParam("offsetPeriods", "20240599999030")
                .UsingGet())
            .RespondWith(WireMock.ResponseBuilders.Response.Create()
                .WithStatusCode(200)
                .WithBody(@"[
                    {
                        ""fromPeriod"": 20240699999030,
                        ""toPeriod"": 20240599999030,
                        ""distance"": -1
                    }
                ]"));

        // Stub for /api/v1/Periods
        Server
.Given(WireMock.RequestBuilders.Request.Create()
        .WithPath("/api/v1/Periods")
        .WithParam("periodicityId", "4")
        .WithParam("from", "May24")
        .WithParam("to", "Jun24")
        .WithParam("limit", "100")
        .UsingGet())
        .RespondWith(WireMock.ResponseBuilders.Response.Create()
        .WithStatusCode(200)
        .WithBody("[]"));
    }

    public void Dispose()
    {
        Server.Stop();
    }
}
