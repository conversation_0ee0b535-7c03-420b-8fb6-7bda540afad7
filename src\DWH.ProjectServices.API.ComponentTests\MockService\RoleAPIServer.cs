﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using WireMock.Server;

namespace DWH.ProjectServices.API.IntegrationTests.MockService
{
    public class RoleAPIServer
    {
        public WireMockServer Server { get; }

        public RoleAPIServer()
        {
            Server = WireMockServer.Start();
            ConfigureWireMockStubs(Server);
        }

        private void ConfigureWireMockStubs(WireMockServer server)
        {
            server
                .Given(WireMock.RequestBuilders.Request.Create()
                    .WithPath("/api/userroles/approveduserIds")
                    .UsingGet())
                .RespondWith(WireMock.ResponseBuilders.Response.Create()
                    .WithStatusCode(200)
                    .WithBody("[1, 2, 3, 4]"));
        }

        public void Dispose()
        {
            Server.Stop();
            Server.Dispose();
        }
    }
}
