﻿using DWH.ProjectServices.API.Services.Interfaces;
using Microsoft.AspNetCore.Mvc.Testing;
using System.Collections.Generic;
using System.Net;
using System.Net.Http.Json;
using System.Text.Json;
using Xunit;
using FluentAssertions;
using DWH.ProjectServices.API.Infrastructure.Persistence.Entities;

namespace DWH.ProjectServices.API.IntegrationTests.Presentation.Controllers
{
    public class BaseProjectPanelsControllerCTest : IClassFixture<WebApplicationFactory<Program>>
    {
        private const string PATH = "/api/v1/BaseProjectPanels";
        private const string notFoundPATH = "/api/v1/BaseProjectPanels/1";
        private readonly WebApplicationFactory<Program> _factory;

        public BaseProjectPanelsControllerCTest(WebApplicationFactory<Program> factory)
        {
            _factory = factory;
        }

        [Fact]
        public async Task GetAllAsync_BaseProjectPanels_Returns_Success_Response()
        {
            var client = _factory.CreateClient();

            var successResponse = await client.GetFromJsonAsync<IEnumerable<BaseProjectPanelEntity>>(PATH);
            successResponse.Should().NotBeNull();

            var notfoundresponse = await client.GetAsync(notFoundPATH);

            notfoundresponse.StatusCode.Should().Be(HttpStatusCode.NotFound);


        }


    }
}
