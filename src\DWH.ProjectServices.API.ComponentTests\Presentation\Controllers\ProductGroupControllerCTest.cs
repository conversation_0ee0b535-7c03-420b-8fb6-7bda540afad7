﻿using AutoFixture;
using DWH.ProjectServices.API.Presentation.Controllers;
using DWH.ProjectServices.API.Services;
using DWH.ProjectServices.API.Services.Interfaces;
using DWH.ProjectServices.API.Models.Dtos;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.VisualStudio.TestPlatform.TestHost;
using Moq;
using System.Net;
using System.Text.Json;
using DWH.ProjectServices.API.Models;
using System.Net.Http.Json;

namespace DWH.ProjectServices.API.IntegrationTests.Presentation.Controllers
{
    public class ProductGroupControllerCTest : IClassFixture<WebApplicationFactory<Program>>
    {
        private const string PATH = "api/v1/ProductGroups";

        private readonly WebApplicationFactory<Program> _factory;
        private readonly IFixture _fixture;
        private const string EXPECTED_MEDIA_TYPE = "application/json";


        public ProductGroupControllerCTest(WebApplicationFactory<Program> factory)
        {
            _factory = factory;
            _fixture = new Fixture();
        }

        [Fact]
        public async Task GetAsync_ProductGroup_Returns_Success_Response()
        {
            var client = _factory.CreateClient();
            var body = _fixture.Create<ProductGroupRequest>();

            var response = await client.PostAsJsonAsync(PATH, body);

            var responseContent = await response.Content.ReadAsStringAsync();
            var options = new JsonSerializerOptions(JsonSerializerDefaults.Web);
            var result = JsonSerializer.Deserialize<IEnumerable<ProductGroup>>(responseContent, options);

            response.StatusCode.Should().Be(HttpStatusCode.OK);
            response.Content.Headers.ContentType?.MediaType.Should().Be(EXPECTED_MEDIA_TYPE);
            responseContent.Should().NotBeNull();
            result.Should().NotBeNull();
        }

        [Fact]
        public async Task GetAsync_Description_Returns_Success_Response()
        {
            var client = _factory.CreateClient();
            var body = new DescriptionRequest(4, new int[] { 30738, 40021, 35611 });

            var response = await client.PostAsJsonAsync(Path.Combine(PATH, "Description"), body);

            var responseContent = await response.Content.ReadAsStringAsync();
            var options = new JsonSerializerOptions(JsonSerializerDefaults.Web);
            var result = JsonSerializer.Deserialize<DescriptionResponse>(responseContent, options);

            response.StatusCode.Should().Be(HttpStatusCode.OK);
            response.Content.Headers.ContentType?.MediaType.Should().Be(EXPECTED_MEDIA_TYPE);
            responseContent.Should().NotBeNull();
            result.Should().NotBeNull();
            result?.PeriodicityShortDesc.Equals("1m");
        }
    }
}
