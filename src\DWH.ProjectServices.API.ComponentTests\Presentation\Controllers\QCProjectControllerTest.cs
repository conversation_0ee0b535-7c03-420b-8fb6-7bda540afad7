﻿using AutoFixture;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using Newtonsoft.Json.Linq;
using RabbitMQ.Client;
using System.Net.Http.Json;
using System.Text;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using System.Net;
using System.Collections.ObjectModel;
using System.Text.Json;
using Microsoft.Extensions.DependencyInjection;
using DWH.ProjectServices.API.Presentation.Controllers;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;
using DWH.ProjectServices.API.Infrastructure.Persistence;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ.Constants;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ.Configuration;
using Microsoft.AspNetCore.Http.HttpResults;
using DWH.ProjectServices.API.Domain.Enum;
using System;
namespace DWH.ProjectServices.API.IntegrationTests.Presentation.Controllers
{


    public class QCProjectControllerTest : IClassFixture<WebApplicationFactory<Program>>
    {

        private readonly WebApplicationFactory<Program> _factory;
        private readonly IChannel _channel;
        private readonly QCProjectsController _qcProjectController;
        private readonly IFixture _fixture;
        private const string PATH = "/api/v1/qcprojects/qcperiod";


        public QCProjectControllerTest(WebApplicationFactory<Program> factory)
        {
            _factory = factory;
            _fixture = new Fixture();
        }

        [Fact]
        public async Task AddAsync_QCPeriod_SendToRabbitMQ_SuccessfullySendsMessage()
        {
            using (var scope = _factory.Services.CreateScope())
            {
                var client = _factory.CreateClient();
                var dbContext = scope.ServiceProvider.GetRequiredService<PostgreSqlDbContext>();
                var specificQCProjectId = 1;
                _fixture.Customize<QCPeriodCreateRequest>(composer =>
                    composer.With(qc => qc.QCProjectId, specificQCProjectId)
                    .With(qc => qc.CreatedBy, "testUser")
                    .With(qc => qc.UpdatedBy, "testUser")
                );
                var body = _fixture.Create<QCPeriodCreateRequest>();
                var initialQCPeriodCount = await dbContext.QCPeriod.CountAsync();

                // Add the userName to the request header
                client.DefaultRequestHeaders.Add("userName", "testUser");

                // ACT
                var result = await client.PostAsJsonAsync(PATH, body);
                result.Should().NotBeNull();
                result.StatusCode.Should().Be(HttpStatusCode.Created);

                // Get the ID from the result
                var responseContent = await result.Content.ReadFromJsonAsync<QCPeriodResponse>();
                responseContent.Should().NotBeNull();

                // Extract the ID from the response
                var createdId = responseContent.Id;
                var lastMessage = GetLastMessageFromQueue(RMQConstants.ProjectServicesQueue);

                var lastMessageObject = JObject.Parse(lastMessage);
                var lastMessageId = (int)lastMessageObject["SyncingEntityId"];
                var finalQCPeriodCount = await dbContext.QCPeriod.CountAsync();

                finalQCPeriodCount.Should().Be(initialQCPeriodCount + 1);
                // Assert
                lastMessage.Should().NotBeNull(); 
                lastMessageId.Should().Be(createdId);

            }
        }

        [Fact]
        public async Task UpdateAsync_QCPeriod_SendToRabbitMQ_SuccessfullySendsMessage()
        {
            using (var scope = _factory.Services.CreateScope())
            {
                var client = _factory.CreateClient();
                var dbContext = scope.ServiceProvider.GetRequiredService<PostgreSqlDbContext>();
                var qcperiodId = 14;
                string PATH = $"/api/v1/qcprojects/qcperiods/{qcperiodId}";

                // Set the userName for the request header
                client.DefaultRequestHeaders.Add("userName", "testUser");

                _fixture.Customize<QCPeriodEditRequest>(qcp => qcp.With(x => x.Periods, new Collection<RefPeriodsEditRequest>
        {
            new RefPeriodsEditRequest
            {
                index = 1,
                RefProjectId = 10,
                RefPeriodId = 10,
            }
        }));

                var qcPeriodEditRequest = _fixture.Create<QCPeriodEditRequest>();

                // Act
                var result = await client.PutAsJsonAsync(PATH, qcPeriodEditRequest);

                // Assert
                result.StatusCode.Should().Be(HttpStatusCode.OK);
                result.Should().NotBeNull();

                // Get the ID from the result
                var responseContent = await result.Content.ReadFromJsonAsync<QCPeriodEditResponse>();
                responseContent.Should().NotBeNull();

                // Extract the ID from the response

                var SyncId = "1-165";
                var lastMessage = GetLastMessageFromQueue(RMQConstants.ProjectServicesQueue);

                var lastMessageObject = JObject.Parse(lastMessage);
                var lastMessageId = (string)lastMessageObject["SyncingEntityId"];

                // Assert
                // Assert
                lastMessage.Should().NotBeNull(); 
                lastMessageId.Should().Be(SyncId); 

            }
        }


        [Fact]
        public async Task UpdateAsync_QCProject_SendToRabbitMQ_SuccessfullySendsMessage()
        {
            var client = _factory.CreateClient();
            var qcProjectid = 1;

            // Set the userName for the request header
            client.DefaultRequestHeaders.Add("userName", "testUser");

            _fixture.Customize<QCProjectEditRequest>(composer =>
                composer.With(qc => qc.ResetCorrectionTypeId, 1));

            var qcProjectEditRequest = _fixture.Create<QCProjectEditRequest>();
            string PATH = $"/api/v1/qcprojects/{qcProjectid}";
            var result = await client.PutAsJsonAsync(PATH, qcProjectEditRequest);
            result.StatusCode.Should().Be(HttpStatusCode.OK);
            result.Should().NotBeNull();
            // Get the ID from the result
            var responseContent = await result.Content.ReadFromJsonAsync<QCProjectEditResponse>();
            responseContent.Should().NotBeNull();

            // Extract the ID from the response
            var createdId = qcProjectid;
            var lastMessage = GetLastMessageFromQueue(RMQConstants.ProjectServicesQueue);

            var lastMessageObject = JObject.Parse(lastMessage);
            var lastMessageId = (int)lastMessageObject["SyncingEntityId"];

            // Assert
            lastMessage.Should().NotBeNull(); 
            lastMessageId.Should().Be(createdId); 
        }


        [Fact]
        public async Task DeleteAsync_QCPeriod_SendToRabbitMQ_SuccessfullySendsMessage()
        {
            using (var scope = _factory.Services.CreateScope())
            {
                var client = _factory.CreateClient();
                var dbContext = scope.ServiceProvider.GetRequiredService<PostgreSqlDbContext>();
                var httpClient = _factory.CreateClient();
                var SyncId = "1-117";
                List<long> qcPeriodIds = new List<long> { 4 };
                _fixture.Customize<QCPeriodDeleteRequest>(q => q.With(x => x.Ids, qcPeriodIds));
                var body = _fixture.Create<QCPeriodDeleteRequest>();
                var initialQCPeriodCount = await dbContext.QCPeriod.CountAsync();
                var stringContent = new StringContent(JsonSerializer.Serialize(body), Encoding.UTF8, "application/json");
                var request = new HttpRequestMessage(HttpMethod.Delete, $"/api/v1/qcprojects/qcperiods");

                request.Content = stringContent;

                var response = await httpClient.SendAsync(request);

                response.StatusCode.Should().Be(HttpStatusCode.MultiStatus);
                var lastMessage = GetLastMessageFromQueue(RMQConstants.ProjectServicesQueue);

                var lastMessageObject = JObject.Parse(lastMessage);
                var syncingEntityIdArray = (JArray)lastMessageObject["SyncingEntityId"];
                var lastMessageId = syncingEntityIdArray[0].ToString();
                var finalQCPeriodCount = await dbContext.QCPeriod.CountAsync();
                // Assert
                lastMessage.Should().NotBeNull();
                lastMessageId.Should().Be(SyncId);
                finalQCPeriodCount.Should().Be(initialQCPeriodCount - 1);
            }
        }

        private string GetLastMessageFromQueue(string queueName)
        {
            _channel.QueueDeclareAsync(queue: queueName,
                                  durable: true,
                                  exclusive: false,
                                  autoDelete: false,
                                  arguments: null);

            BasicGetResult? result = null;
            string lastMessage = string.Empty;

            while ((result = _channel.BasicGetAsync(queueName, true).Result) != null)
            {
                var body = result.Body.ToArray();
                lastMessage = Encoding.UTF8.GetString(body);
            }

            return string.IsNullOrEmpty(lastMessage) ? string.Empty : lastMessage;
        }

        [Fact]
        public async Task GetQCProjectAsync_Returns_Filtered_ResponseSuccess()
        {
            
            // Arrange
            var client = _factory.CreateClient();


            //Add Baseproject with specific country id to test later
            client.DefaultRequestHeaders.Add("userName", "IntegrationTest");
            _fixture.Customize<BaseProjectCreateRequest>(b => b.With(x => x.PanelId, 1).With(x => x.CountryId, 15).With(x => x.PeriodicityId, 1).With(x => x.ResetCorrectionTypeId, 3));

            var baseProjectbody = _fixture.Create<BaseProjectCreateRequest>();

            var createdresponse = await client.PostAsJsonAsync("/api/v1/baseprojects", baseProjectbody);
            var createdresponseContent = await createdresponse.Content.ReadAsStringAsync();

            var options = new JsonSerializerOptions(JsonSerializerDefaults.Web);
            var responseObject = JsonSerializer.Deserialize<BaseProjectResponse>(createdresponseContent, options);

            //Now check that baseprojects qc id with same country with new endpoint
            var qcprojectCheckBody = new QCProjectCountryRequest
            {
                QCProjectIds = new int[] { responseObject.QCProjects.Id },
                CountryIds = new int[] { 15 }
            };
          
            var filteredResponse = await client.PostAsJsonAsync("/api/v1/qcprojects/qcproject", qcprojectCheckBody);
            var filteredResponseContent = await filteredResponse.Content.ReadAsStringAsync();
            var filteredResult = JsonSerializer.Deserialize<List<int>>(filteredResponseContent, options);

            // Assert
            filteredResponse.StatusCode.Should().Be(HttpStatusCode.OK);
            filteredResult.Should().NotBeNull();
            filteredResult.Should().Contain(responseObject.QCProjects.Id);
        }

        [Fact]
        public async Task GetQCProjectAsync_Returns_Filtered_ResponseFailed()
        {

            // Arrange
            var client = _factory.CreateClient();


            //Add Baseproject with specific country id to test later
            client.DefaultRequestHeaders.Add("userName", "IntegrationTest");
            _fixture.Customize<BaseProjectCreateRequest>(b => b.With(x => x.PanelId, 1).With(x => x.CountryId, 15).With(x => x.PeriodicityId, 1).With(x => x.ResetCorrectionTypeId, 3));

            var baseProjectbody = _fixture.Create<BaseProjectCreateRequest>();

            var createdresponse = await client.PostAsJsonAsync("/api/v1/baseprojects", baseProjectbody);
            var createdresponseContent = await createdresponse.Content.ReadAsStringAsync();

            var options = new JsonSerializerOptions(JsonSerializerDefaults.Web);
            var responseObject = JsonSerializer.Deserialize<BaseProjectResponse>(createdresponseContent, options);

            //Now check that baseprojects qc id with different country with new endpoint
            var qcprojectCheckBody = new QCProjectCountryRequest
            {
                QCProjectIds = new int[] { responseObject.QCProjects.Id },
                CountryIds = new int[] { 25 }
            };

            var filteredResponse = await client.PostAsJsonAsync("/api/v1/qcprojects/qcproject", qcprojectCheckBody);
          
            // Assert
            filteredResponse.StatusCode.Should().Be(HttpStatusCode.NotFound);
        }

        [Fact]
        public async Task UpdateAsync_QCProject_Should_UpdateAndSendToRabbitMQ()
        {
            // Arrange
            using var scope = _factory.Services.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<PostgreSqlDbContext>();
            var client = _factory.CreateClient();
            var qcProjectId = 1;
            string PATH = $"/api/v1/qcprojects/{qcProjectId}";

            // Set Authorization Headers
            client.DefaultRequestHeaders.Add("userName", "testUser");
            client.DefaultRequestHeaders.Add("Custom-Countryid", "47"); // Ensure this is a valid country

            _fixture.Customize<QCProjectEditRequest>(composer =>
                composer.With(qc => qc.ResetCorrectionTypeId, 1)
                        .With(qc => qc.IsAutoLoad, true)
                        .With(qc => qc.SQCMode, 2)
                        .With(qc => qc.IsAutomatedPriceCheck, false)
            );

            var qcProjectEditRequest = _fixture.Create<QCProjectEditRequest>();

            // Act
            var result = await client.PutAsJsonAsync(PATH, qcProjectEditRequest);

            // Assert HTTP response
            result.StatusCode.Should().Be(HttpStatusCode.OK);
            result.Should().NotBeNull();

            // Verify Response Content
            var responseContent = await result.Content.ReadFromJsonAsync<QCProjectEditResponse>();
            responseContent.Should().NotBeNull();
            responseContent.Id.Should().Be(qcProjectId);

            // Fetch the latest OutBoxItem (should be in Pending state)
            var outboxItem = await dbContext.OutBoxItem
                .OrderByDescending(oi => oi.CreatedAt)
                .FirstOrDefaultAsync(oi => oi.TypeId == ProjectMessageType.QCProjectUpdate.ToString());

            // Assert OutBoxItem creation
            outboxItem.Should().NotBeNull();
            outboxItem.TypeId.Should().Be(ProjectMessageType.QCProjectUpdate.ToString());
            outboxItem.Status.Should().Be(OutboxStatus.Pending); // Initially, it should be Pending


            // Refresh the OutBoxItem from the database after background service execution
            outboxItem = await dbContext.OutBoxItem
                .OrderByDescending(oi => oi.CreatedAt)
                .FirstOrDefaultAsync(oi => oi.TypeId == ProjectMessageType.QCProjectUpdate.ToString());

            // Assert OutBoxItem existence
            outboxItem.Should().NotBeNull();
            outboxItem.TypeId.Should().Be(ProjectMessageType.QCProjectUpdate.ToString());
        }




        public void Dispose()
        {
            _channel?.Dispose();
        }
    }
}