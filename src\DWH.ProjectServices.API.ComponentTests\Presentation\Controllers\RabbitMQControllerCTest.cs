﻿using RabbitMQ.Client;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Logging;
using AutoMapper;
using DWH.ProjectServices.API.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using DWH.ProjectServices.API.Models.Dtos;
using AutoFixture;
using System.Text;
using Newtonsoft.Json.Linq;
using DWH.ProjectServices.API.Presentation.Controllers;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;
using DWH.ProjectServices.API.Infrastructure.Persistence;
using DWH.ProjectServices.API.Infrastructure.Persistence.Entities;
using DWH.ProjectServices.API.Infrastructure.Persistence.Repositories;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ.Constants;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ.Configuration;
using DWH.ProjectServices.API.Services.Helper.Interface;
using DWH.ProjectServices.API.Services.Helper;
using System.Net.Http;
using Microsoft.AspNetCore.Mvc.Testing;
using Docker.DotNet.Models;
using Microsoft.Extensions.DependencyInjection;
using System.IO;
using System.Net.Http.Json;

namespace DWH.ProjectServices.API.IntegrationTests.Presentation.Controllers
{
    public class RabbitMQSenderIntegrationTests : IDisposable
    {
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private readonly IConnection _connection;
        private readonly IChannel _channel;
        private readonly RabbitMQSender _rabbitMQSender;
        private readonly IFixture _fixture;
        private readonly WebApplicationFactory<Program> _factory;
        private const string PATH = "/api/v1/baseprojects";

        public RabbitMQSenderIntegrationTests(WebApplicationFactory<Program> factory)
        {
            _factory = factory;
            _fixture = new Fixture();

            var rabbitMQSettings = Options.Create(new RabbitMQSettings
            {
                HostName = "localhost", // RabbitMQ Docker container host
                UserName = "guest",
                Password = "guest",
                VirtualHost = "vhbdxt1"
            });

            var connectionFactory = new RabbitMQConnectionFactory(rabbitMQSettings);
            _connection = connectionFactory.CreateConnectionAsync().GetAwaiter().GetResult();
            _channel = _connection.CreateChannelAsync().GetAwaiter().GetResult();

            var loggerFactory = LoggerFactory.Create(builder =>
            {
                builder.AddConsole();
            });

            var logger = loggerFactory.CreateLogger<RabbitMQSender>();
            _rabbitMQSender = new RabbitMQSender(connectionFactory, logger);

            var connectionString = "Server=localhost;Port=5432;Database=projectservices_db;UserId=postgres;Password=mysecretpassword";
            var oracleconnectionString = "Server=localhost;Port=1521;Database=projectservices_db;UserId=sys;Password=password;DBA Privilege=SYSDBA";

            var dbContextOptions = new DbContextOptionsBuilder<PostgreSqlDbContext>()
                .UseNpgsql(connectionString)
                .Options;

            var oracledbContextOptions = new DbContextOptionsBuilder<OracleDbContext>()
              .UseOracle(oracleconnectionString)
              .Options;
            var postDbContext = new PostgreSqlDbContext(dbContextOptions);
            var oracleDbContext = new OracleDbContext(oracledbContextOptions);

            var mapperConfiguration = new MapperConfiguration(cfg =>
            {
                cfg.CreateMap<BaseProjectCreateRequest, BaseProjectEntity>().ReverseMap();
                cfg.CreateMap<BaseProjectResponse, BaseProjectEntity>().ReverseMap();
                cfg.CreateMap<BaseProjectPredecessorModelDto, BaseProjectPredecessorEntity>().ReverseMap();
                cfg.CreateMap<BaseProjectProductGroupModelDto, BaseProjectProductGroupEntity>().ReverseMap();
                cfg.CreateMap<BaseProjectEntity, BaseProjectPredecessorResponse>().ReverseMap();
                cfg.CreateMap<BaseProjectEditRequest, BaseProjectEntity>().ReverseMap();
                cfg.CreateMap<BaseProjectEditResponse, BaseProjectEntity>().ReverseMap();
            });

            _serviceScopeFactory = _factory.Services.GetRequiredService<IServiceScopeFactory>();

            var mapper = mapperConfiguration.CreateMapper();
            var retailerSeperationrepo = new RetailerSeperationRepository(postDbContext, mapper, _serviceScopeFactory);
            var baseProjectRepository = new BaseProjectRepository(oracleDbContext,postDbContext, mapper, retailerSeperationrepo, _serviceScopeFactory);
        }

        [Fact]
        public async Task SendToRabbitMQ_SuccessfullySendsMessage()
        {
            using (var scope = _factory.Services.CreateScope())
            {
                var client = _factory.CreateClient();
                    _fixture.Customize<BaseProjectCreateRequest>(composer => composer
                .With(request => request.ResetCorrectionTypeId, 1)
                .With(request => request.PanelId, 2));

                var userName = "shayan";

                var body = _fixture.Create<BaseProjectCreateRequest>();
                var result = await client.PostAsJsonAsync(PATH, body);

                result.Should().BeOfType<CreatedAtActionResult>()
                    .Which.StatusCode.Should().Be(StatusCodes.Status201Created);

                // Get the ID from the result
                var createdObject = result.Should().BeOfType<CreatedAtActionResult>().Which.Value as BaseProjectResponse;
                var createdId = createdObject?.Id;


                var lastMessage = GetLastMessageFromQueue(RMQConstants.ProjectServicesQueue);


                var lastMessageObject = JObject.Parse(lastMessage);
                var lastMessageId = (int)lastMessageObject["SyncingEntityId"];

                lastMessage.Should().NotBeNull();
                lastMessageId.Should().Be(createdId);
            }
        }

        private string GetLastMessageFromQueue(string queueName)
        {
            _channel.QueueDeclareAsync(queue: queueName,
                                  durable: true,
                                  exclusive: false,
                                  autoDelete: false,
                                  arguments: null);

            BasicGetResult? result = null;
            string lastMessage = string.Empty;

            while ((result = _channel.BasicGetAsync(queueName, true).Result) != null)
            {
                var body = result.Body.ToArray();
                lastMessage = Encoding.UTF8.GetString(body);
            }
            return string.IsNullOrEmpty(lastMessage) ? string.Empty : lastMessage;
        }


        public void Dispose()
        {
            _channel?.Dispose();
            _connection?.Dispose();
            _rabbitMQSender?.DisposeAsync();
        }
    }
}
