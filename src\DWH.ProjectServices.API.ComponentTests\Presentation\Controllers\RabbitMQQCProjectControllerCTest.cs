﻿using System.Collections.ObjectModel;
using System.Net;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using AutoFixture;
using AutoMapper;
using DWH.ProjectServices.API.Infrastructure.Persistence;
using DWH.ProjectServices.API.Infrastructure.Persistence.Entities;
using DWH.ProjectServices.API.Infrastructure.Persistence.Repositories;
using DWH.ProjectServices.API.Infrastructure.Persistence.Repositories.Interfaces;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ.Configuration;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ.Constants;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;
using DWH.ProjectServices.API.Presentation.Controllers;
using DWH.ProjectServices.API.Services;
using DWH.ProjectServices.API.Services.Helper;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using Newtonsoft.Json.Linq;
using RabbitMQ.Client;
namespace DWH.ProjectServices.API.IntegrationTests.Presentation.Controllers
{


    public class RabbitMQQCProjectControllerCTest : IClassFixture<WebApplicationFactory<Program>>
    {

        private readonly WebApplicationFactory<Program> _factory;
        private readonly IConnection _connection;
        private readonly IChannel _channel;
        private readonly RabbitMQSender _rabbitMQSender;
        private readonly QCProjectsController _qcProjectController;
        private readonly IFixture _fixture;
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private readonly Mock<IOutBoxItemRepository> _mockOutBoxItemRepository;
        private const string PATH = "/api/v1/qcprojects/qcperiod";


        public RabbitMQQCProjectControllerCTest(WebApplicationFactory<Program> factory)
        {
            _factory = factory;
            _fixture = new Fixture();

            var rabbitMQSettings = Options.Create(new RabbitMQSettings
            {
                HostName = "localhost",
                UserName = "guest",
                Password = "guest",
                VirtualHost = "/"
            });

            var connectionFactory = new RabbitMQConnectionFactory(rabbitMQSettings);
            _connection = connectionFactory.CreateConnectionAsync().GetAwaiter().GetResult();
            _channel = _connection.CreateChannelAsync().GetAwaiter().GetResult();

            var loggerFactory = LoggerFactory.Create(builder =>
            {
                builder.AddConsole();
            });

            var logger = loggerFactory.CreateLogger<RabbitMQSender>();
            _mockOutBoxItemRepository = new Mock<IOutBoxItemRepository>();
            _rabbitMQSender = new RabbitMQSender(connectionFactory, logger);
            var connectionString = "Server=localhost;Port=5433;Database=ProjectServices;UserId=postgres;Password=mysecretpassword";
            var dbContextOptions = new DbContextOptionsBuilder<PostgreSqlDbContext>()
                .UseNpgsql(connectionString)
                .Options;

            var postDbContext = new PostgreSqlDbContext(dbContextOptions);



            var mapperConfiguration = new MapperConfiguration(cfg =>
            {
                cfg.CreateMap<QCProjectEditResponse, QCProjectEntity>().ReverseMap();
                cfg.CreateMap<QCPeriodCreateRequest, QCPeriodEntity>().ReverseMap();
                cfg.CreateMap<ReferencePeriodRequest, PeriodEntity>().ReverseMap();
                cfg.CreateMap<StockInitiliazationRequest, StockInitializationEntity>().ReverseMap();
                cfg.CreateMap<QCPeriodResponse, QCPeriodEntity>().ReverseMap();
                cfg.CreateMap<QCPeriodEditResponse, QCPeriodEntity>().ReverseMap();
                cfg.CreateMap<QCPeriodModelDto, QCPeriodEntity>()
                             .ForMember(dest => dest.CreatedWhen, opt => opt.MapFrom(src => src.DateOfCreation))
                             .ReverseMap();
            });

            var mapper = mapperConfiguration.CreateMapper();

            _serviceScopeFactory = _factory.Services.GetRequiredService<IServiceScopeFactory>();

            var qcProjectRepository = new QCProjectRepository(postDbContext, mapper, _serviceScopeFactory);

            var qcProjectService = new QCProjectService(qcProjectRepository, _mockOutBoxItemRepository.Object);

            _qcProjectController = new QCProjectsController(mapper, qcProjectService);




        }

        [Fact]
        public async Task AddAsync_QCPeriod_SendToRabbitMQ_SuccessfullySendsMessage()
        {

            var client = _factory.CreateClient();
            var specificQCProjectId = 1;
            _fixture.Customize<QCPeriodCreateRequest>(composer =>
                composer.With(qc => qc.QCProjectId, specificQCProjectId));
            var body = _fixture.Create<QCPeriodCreateRequest>();

            //ACT
            var result = await client.PostAsJsonAsync(PATH, body);
            result.Should().NotBeNull();
            result.StatusCode.Should().Be(HttpStatusCode.Created);

            // Get the ID from the result
            var responseContent = await result.Content.ReadFromJsonAsync<QCPeriodResponse>();
            responseContent.Should().NotBeNull();

            // Extract the ID from the response
            var createdId = responseContent.Id;
            var lastMessage = GetLastMessageFromQueue(RMQConstants.ProjectServicesQueue);

            var lastMessageObject = JObject.Parse(lastMessage);
            var lastMessageId = (int)lastMessageObject["SyncingEntityId"];

            // Assert
            lastMessage.Should().NotBeNull();
            lastMessageId.Should().Be(createdId);
        }

        [Fact]
        public async Task UpdateAsync_QCPeriod_SendToRabbitMQ_SuccessfullySendsMessage()
        {
            var client = _factory.CreateClient();
            var qcperiodId = 18;
            string PATH = $"/api/v1/qcprojects/qcperiods/{qcperiodId}";


            _fixture.Customize<QCPeriodEditRequest>(qcp => qcp.With(x => x.Periods, new Collection<RefPeriodsEditRequest>
                                                                          {
                                                                              new RefPeriodsEditRequest
                                                                              {
                                                                                  index = 1,
                                                                                  RefProjectId = 10,
                                                                                  RefPeriodId = 10,
                                                                              }
                                                                          }));

            var qcPeriodEditRequest = _fixture.Create<QCPeriodEditRequest>();

            // Act
            var result = await client.PutAsJsonAsync(PATH, qcPeriodEditRequest);

            // Assert
            result.StatusCode.Should().Be(HttpStatusCode.OK);
            result.Should().NotBeNull();

            // Get the ID from the result
            var responseContent = await result.Content.ReadFromJsonAsync<QCPeriodEditResponse>();
            responseContent.Should().NotBeNull();

            // Extract the ID from the response
            var createdId = qcperiodId;
            var lastMessage = GetLastMessageFromQueue(RMQConstants.ProjectServicesQueue);

            var lastMessageObject = JObject.Parse(lastMessage);
            var lastMessageId = (int)lastMessageObject["SyncingEntityId"];

            // Assert
            lastMessage.Should().NotBeNull();
            lastMessageId.Should().Be(createdId);
        }



        [Fact]
        public async Task UpdateAsync_QCProject_SendToRabbitMQ_SuccessfullySendsMessage()
        {
            var client = _factory.CreateClient();
            var qcProjectid = 6;
            _fixture.Customize<QCProjectEditRequest>(composer =>
                composer.With(qc => qc.ResetCorrectionTypeId, 1));
            var qcProjectEditRequest = _fixture.Create<QCProjectEditRequest>();
            string PATH = $"/api/v1/qcprojects/{qcProjectid}";
            var result = await client.PutAsJsonAsync(PATH, qcProjectEditRequest);
            result.StatusCode.Should().Be(HttpStatusCode.OK);
            result.Should().NotBeNull();
            // Get the ID from the result
            var responseContent = await result.Content.ReadFromJsonAsync<QCPeriodEditResponse>();
            responseContent.Should().NotBeNull();

            // Extract the ID from the response
            var createdId = qcProjectid;
            var lastMessage = GetLastMessageFromQueue(RMQConstants.ProjectServicesQueue);

            var lastMessageObject = JObject.Parse(lastMessage);
            var lastMessageId = (int)lastMessageObject["SyncingEntityId"];

            // Assert
            lastMessage.Should().NotBeNull();
            lastMessageId.Should().Be(createdId);
        }

        [Fact]
        public async Task DeleteAsync_QCPeriod_SendToRabbitMQ_SuccessfullySendsMessage()
        {
            var httpClient = _factory.CreateClient();
            var SyncId = "1-1";
            List<long> qcPeriodIds = new List<long> { 22 };
            _fixture.Customize<QCPeriodDeleteRequest>(q => q.With(x => x.Ids, qcPeriodIds));
            var body = _fixture.Create<QCPeriodDeleteRequest>();

            var stringContent = new StringContent(JsonSerializer.Serialize(body), Encoding.UTF8, "application/json");
            var request = new HttpRequestMessage(HttpMethod.Delete, $"/api/v1/qcprojects/qcperiods");

            request.Content = stringContent;

            var response = await httpClient.SendAsync(request);

            response.StatusCode.Should().Be(HttpStatusCode.MultiStatus);
            var lastMessage = GetLastMessageFromQueue(RMQConstants.ProjectServicesQueue);

            var lastMessageObject = JObject.Parse(lastMessage);
            var syncingEntityIdArray = (JArray)lastMessageObject["SyncingEntityId"];
            var lastMessageId = syncingEntityIdArray[0].ToString();

            // Assert
            lastMessage.Should().NotBeNull();
            lastMessageId.Should().Be(SyncId);
        }

        private string GetLastMessageFromQueue(string queueName)
        {
            _channel.QueueDeclareAsync(queue: queueName,
                                   durable: true,
                                   exclusive: false,
                                   autoDelete: false,
                                   arguments: null);

            BasicGetResult? result = null;
            string lastMessage = string.Empty;

            while ((result = _channel.BasicGetAsync(queueName, true).Result) != null)
            {
                var body = result.Body.ToArray();
                lastMessage = Encoding.UTF8.GetString(body);
            }

            return string.IsNullOrEmpty(lastMessage) ? string.Empty : lastMessage;
        }
    }
}