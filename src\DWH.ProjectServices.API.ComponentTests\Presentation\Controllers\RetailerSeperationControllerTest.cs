﻿using AutoFixture;
using DWH.ProjectServices.API.Infrastructure.Persistence;
using DWH.ProjectServices.API.Infrastructure.WebServiceClient.Interfaces;
using DWH.ProjectServices.API.Infrastructure.WebServiceClient;
using DWH.ProjectServices.API.Models;
using DWH.ProjectServices.API.Models.Dtos;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using MongoDB.Driver.Core.Configuration;
using System.Net;
using System.Net.Http.Json;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using WireMock.Server;
using DWH.ProjectServices.API.Services.Helper;
using Microsoft.Extensions.Options;
using DWH.ProjectServices.API.Services.Helper.Interface;
using Google.Protobuf.WellKnownTypes;
using System.Net.Http.Headers;
using WireMock.Settings;
using DWH.ProjectServices.API.IntegrationTests.MockService;
using ProtoBuf.Serializers;
using DWH.ProjectServices.API.Services.Interfaces;
using Moq;
using DWH.ProjectServices.API.Infrastructure.Persistence.Entities;
using Microsoft.AspNetCore.Mvc;
using DWH.ProjectServices.API.Domain.Models;
using Microsoft.AspNetCore.Http;
using FluentAssertions;
using DWH.ProjectServices.API.Domain.Enum;


namespace DWH.ProjectServices.API.IntegrationTests.Presentation.Controllers
{
    public class RetailerSeperationControllerTest : IClassFixture<APIFactory>
    {
        private const string PATH = "/api/v1/retailerseperation";

        private readonly APIFactory _factory;
        private readonly WireMockManager _wireMockManager;
        private readonly Fixture _fixture;

        public RetailerSeperationControllerTest(APIFactory factory)
        {
            _fixture = new Fixture();
            _factory = factory;
            _wireMockManager = new WireMockManager();
        }

        [Fact]
        public async Task AddAsync_ValidRequest_ReturnsCreated()
        {
            // Arrange
            var _client = _factory.CreateClient();
            _fixture.Customize<RetailerSeperationCreateRequest>(c => c
                .With(p => p.RequestStatusId, 1));
            _fixture.Customize<RetailerSeperationRequestDetailModelDto>(c => c
               .With(p => p.RequestStatusId, 1));

            var request = _fixture.Create<RetailerSeperationCreateRequest>();

            var content = new StringContent(JsonSerializer.Serialize(request), Encoding.UTF8, "application/json");
            _client.DefaultRequestHeaders.Add("userName", "testUser");

            // Act
            var response = await _client.PostAsync(PATH, content);

            // Assert
            response.IsSuccessStatusCode.Should().BeTrue();
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.Created);

            var result = JsonSerializer.Deserialize<RetailerSeperationResponse>(await response.Content.ReadAsStringAsync());
            result.Should().NotBeNull();
        }


        [Fact]
        public async Task AddAsync_InValidRequest_ReturnsBadRequest()
        {
            // Arrange
            var _client = _factory.CreateClient();

            RetailerSeperationCreateRequest request = new RetailerSeperationCreateRequest()
            {
                FromPeriodId = 123,
                ToPeriodId = 124,
                ResetCorrection=true,
                Extrapolation=false
            };

            var content = new StringContent(JsonSerializer.Serialize(request), Encoding.UTF8, "application/json");

            // Act
            var response = await _client.PostAsync(PATH, content);

            // Assert
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.BadRequest);
        }

        [Fact]
        public async Task AddAsync_InValidRequestMorethan20SourceBPs_ReturnsBadRequest()
        {
            // Arrange
            var _client = _factory.CreateClient();

            RetailerSeperationCreateRequest request = new RetailerSeperationCreateRequest()
            {
                FromPeriodId = 123,
                ToPeriodId = 124,
                ResetCorrection = true,
                Extrapolation = false,
                RetailerSeperations = _fixture.CreateMany<RetailerSeperationModelDto>(21).ToList()
            };

            var content = new StringContent(JsonSerializer.Serialize(request), Encoding.UTF8, "application/json");

            // Act
            var response = await _client.PostAsync(PATH, content);

            // Assert
            response.StatusCode.Should().Be(System.Net.HttpStatusCode.BadRequest);
        }

        [Fact]
        public async Task GetAllAsync_RetailerSeperations_Returns_Success_Response()
        {
            // Arrange
            var client = _factory.CreateClient();
            var body = new RetailerSeperationListRequest() { RequestStatusIds = new int[] {1,2,3,4} };

            // Act
            var response = await client.PostAsJsonAsync(PATH +"/List", body);

            var responseContent = await response.Content.ReadAsStringAsync();
            var options = new JsonSerializerOptions(JsonSerializerDefaults.Web);

            var result = JsonSerializer.Deserialize<RetailerSeperationListResponse>(responseContent, options);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            responseContent.Should().NotBeNull();
            result.Should().NotBeNull();
        }

        [Fact]
        public async Task GetAllAsync_RetailerSeperations_When_EmptyResponse_Returns_NotFound_With_ProblemDetails()
        {
            // Arrange
            var client = _factory.CreateClient();
            var body = new RetailerSeperationListRequest() { RequestStatusIds = new int[] { -1 } };

            // Act
            var response = await client.PostAsJsonAsync(PATH + "/List", body);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.NotFound);

            var responseContent = await response.Content.ReadAsStringAsync();
            responseContent.Should().NotBeNullOrEmpty();

            var problemDetails = JsonSerializer.Deserialize<ProblemDetails>(responseContent, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            problemDetails.Should().NotBeNull();
            problemDetails.Status.Should().Be(404);
            problemDetails.Title.Should().Be("ENTITY_NOT_EXISTS");
        }

        [Fact]
        public async Task GetUserList_RetailerSeperations_Returns_Success_Response()
        {
            // Arrange
            var client = _factory.CreateClient();

            // Act
            var response = await client.GetAsync(PATH + "/Userlist");

            var responseContent = await response.Content.ReadAsStringAsync();
            var options = new JsonSerializerOptions(JsonSerializerDefaults.Web);

            var result = JsonSerializer.Deserialize<IReadOnlyList<string>>(responseContent, options);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            responseContent.Should().NotBeNull();
            result.Should().NotBeNull();
            result.Should().BeOfType<List<string>>();
        }


        [Fact]
        public async Task CreateAsyncIRSeparation_ValidRequest_ReturnsMultiStatus()

        {
            using (var scope = _factory.Services.CreateScope())
            {   
                // Arrange
                var dbContext = scope.ServiceProvider.GetRequiredService<PostgreSqlDbContext>();
                var IRBaseProjectId = await CreateBaseProjectIR();
                var IRRequestId = await AddAsyncIRRequest(IRBaseProjectId);

                var retailerSeperationRequestIds = new List<int> { IRRequestId }; 
                var userName = "testUser";
                var irSeparationRequest = new IRSeparationRequest { retailerSeperationRequestIds = new List<int> { IRRequestId } };
                var _client = _factory.CreateClient();
                var initialOutboxCount = await dbContext.OutBoxItem.CountAsync();


                // Act
                _client.DefaultRequestHeaders.Add("userName", userName);
                var response = await _client.PostAsync(
                    PATH + "/IRSeparation",
                    new StringContent(JsonSerializer.Serialize(irSeparationRequest), Encoding.UTF8, "application/json")
                );

                // Assert
                response.IsSuccessStatusCode.Should().BeTrue();  // Ensure success
                response.StatusCode.Should().Be(HttpStatusCode.MultiStatus); // Ensure Multi-Status response
                var finalOutboxCount = await dbContext.OutBoxItem.CountAsync();
                finalOutboxCount.Should().Be(initialOutboxCount + 1);

            }
        }
        [Fact]
        public async Task CreateAsyncIRSeparation_NoRetailerSeperationRequestIds_ReturnsBadRequest()
        {
            // Arrange
            var irSeparationRequest = new IRSeparationRequest { retailerSeperationRequestIds = new List<int>() }; 
            var userName = "testUser";
            var _client = _factory.CreateClient();
          
            // Act
            _client.DefaultRequestHeaders.Add("userName", userName);
            var response = await _client.PostAsync(
                PATH + "/IRSeparation",
                new StringContent(JsonSerializer.Serialize(irSeparationRequest), Encoding.UTF8, "application/json")
            );

            // Assert
            var responseBody = await response.Content.ReadAsStringAsync();
            var responseRecords = JsonSerializer.Deserialize<IRSeparationRecords>(responseBody);
            responseRecords.IRSeparationRecord.Should().BeNull();
        }

        [Fact]
        public async Task UpdateAsync_ValidRequest_ReturnsSuccess()
        {
            using (var scope = _factory.Services.CreateScope())
            {
                // Arrange
                var retailerSeperationRequestId = new Random().Next(0, 1000) + _fixture.Create<int>();
                var dbContext = scope.ServiceProvider.GetRequiredService<PostgreSqlDbContext>();
                var baseProject = _fixture.Create<BaseProjectEntity>();
                baseProject.PanelId = 1;
                baseProject.DataTypeId = baseProject.DataType.Id;
                baseProject.QCProjects.Id = new Random().Next(0, 1000) + _fixture.Create<int>();
                baseProject.QCProjects.ResetCorrectionTypeId = 1;

                var retailerSeperationRequest = new RetailerSeperationRequestEntity
                {
                    Id = retailerSeperationRequestId,
                    JiraId = "STS-123456",
                    FromPeriodId = 0,
                    ToPeriodId = 0,
                    Extrapolation = false,
                    ResetCorrection = false,
                    RequestStatusId = 1,
                    RetailerSeperations = new List<RetailerSeperationEntity>
                    {
                        new RetailerSeperationEntity
                        {
                            SourceBPId = baseProject.Id,
                            RetailerBPId = 0,
                            RetailerSeperationRequestId = retailerSeperationRequestId,
                            IsError = false
                        }
                    },
                    RetailerSeperationRequestDetails = new List<RetailerSeperationRequestDetailEntity>
                    {
                        new RetailerSeperationRequestDetailEntity
                        {
                            Id = 1,
                            RequestStatusId = 1,
                            RetailerSeperationRequestId = retailerSeperationRequestId,
                            UpdatedBy = "testUser",
                            UpdatedWhen = DateTimeOffset.Now
                        }
                    }
                };

                dbContext.BaseProjects.Add(baseProject);
                dbContext.RetailerSeperationRequests.Add(retailerSeperationRequest);

                await dbContext.SaveChangesAsync();

                _fixture.Customize<RetailerSeperationCreateRequest>(c => c
                    .With(p => p.RequestStatusId, 1)
                    .With(p => p.RetailerSeperations, new List<RetailerSeperationModelDto>() {
                            new RetailerSeperationModelDto
                            {
                                SourceBPId = baseProject.Id
                            }
                    }));
                _fixture.Customize<RetailerSeperationRequestDetailModelDto>(c => c
                   .With(p => p.RequestStatusId, 1));

                var request = _fixture.Create<RetailerSeperationCreateRequest>();

                var content = new StringContent(JsonSerializer.Serialize(request), Encoding.UTF8, "application/json");

                // Act
                var _client = _factory.CreateClient();
                _client.DefaultRequestHeaders.Add("userName", "IntegrationTest");
                var createResponse = await _client.PostAsync(PATH, content);
                createResponse.EnsureSuccessStatusCode();

                var createdProjectContent = await createResponse.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions(JsonSerializerDefaults.Web);
                var createdRetailerSeparation = JsonSerializer.Deserialize<RetailerSeperationResponse>(createdProjectContent, options);

                var editRequest = new RetailerSeperationEditRequest
                {
                    FromPeriodId = 0,
                    ToPeriodId = 0,
                    Extrapolation = false,
                    RetailerSeperations = new List<RetailerSeperationEditModelDto>()
                        {
                            new RetailerSeperationEditModelDto()
                            {
                              SourceBPId = baseProject.Id,
                              IsError = false
                            }
                        },
                    ResetCorrection = false
                };

                // Add headers and send the update request
                _client.DefaultRequestHeaders.Add("Custom-Countryid", baseProject.CountryId.ToString());

                var response = await _client.PutAsJsonAsync(PATH + "/" + createdRetailerSeparation.Id, editRequest);

                var responseContent = await response.Content.ReadAsStringAsync();
                var result = JsonSerializer.Deserialize<BaseProjectEditResponse>(responseContent, options);

                response.StatusCode.Should().Be(HttpStatusCode.OK);

                responseContent.Should().NotBeNull();
                result.Should().NotBeNull();
            }
        }

        [Fact]
        public async Task UpdateAsync_InValidRequest_ReturnsForbidden()
        {
            var _client = _factory.CreateClient();
            _fixture.Customize<RetailerSeperationCreateRequest>(c => c
                .With(p => p.RequestStatusId, 1)
                .With(p => p.RetailerSeperations, new List<RetailerSeperationModelDto>() {
                    new RetailerSeperationModelDto
                    {
                        SourceBPId = 1
                    }
                }));
            _fixture.Customize<RetailerSeperationRequestDetailModelDto>(c => c
               .With(p => p.RequestStatusId, 1));

            var request = _fixture.Create<RetailerSeperationCreateRequest>();

            var content = new StringContent(JsonSerializer.Serialize(request), Encoding.UTF8, "application/json");
            _client.DefaultRequestHeaders.Add("userName", "testUser");

            // Act
            var createResponse = await _client.PostAsync(PATH, content);
            createResponse.EnsureSuccessStatusCode();

            var createdProjectContent = await createResponse.Content.ReadAsStringAsync();
            var options = new JsonSerializerOptions(JsonSerializerDefaults.Web);
            var createdRetailerSeparation = JsonSerializer.Deserialize<RetailerSeperationResponse>(createdProjectContent, options);

            var editRequest = new RetailerSeperationEditRequest
            {
                FromPeriodId = 0,
                ToPeriodId = 0,
                Extrapolation = false,
                RetailerSeperations = new List<RetailerSeperationEditModelDto>()
                {
                    new RetailerSeperationEditModelDto()
                    {
                      SourceBPId = 1,
                      IsError = false
                    }
                },
                ResetCorrection = false
            };

            // Add headers and send the update request
            _client.DefaultRequestHeaders.Add("userName", "testUser");
            _client.DefaultRequestHeaders.Add("Custom-Countryid", "12,13,15");

            var response = await _client.PutAsJsonAsync(PATH + "/" + createdRetailerSeparation.Id, editRequest);

            var responseContent = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<BaseProjectEditResponse>(responseContent, options);

            response.StatusCode.Should().Be(HttpStatusCode.Forbidden);

            responseContent.Should().NotBeNull();
            result.Should().NotBeNull();
        }

        [Fact]
        public async Task GetAsync_RetailerSeperations_Returns_Success_Response()
        {
            // Arrange
            _fixture.Customize<RetailerSeperationCreateRequest>(c => c
                    .With(p => p.RequestStatusId, 1)
                    .With(p => p.RetailerSeperations, new List<RetailerSeperationModelDto>() {
                            new RetailerSeperationModelDto
                            {
                                SourceBPId = new Random().Next(0, 1000) + _fixture.Create<int>()
                            }
                    }));
            _fixture.Customize<RetailerSeperationRequestDetailModelDto>(c => c
               .With(p => p.RequestStatusId, 1));

            var request = _fixture.Create<RetailerSeperationCreateRequest>();

            var content = new StringContent(JsonSerializer.Serialize(request), Encoding.UTF8, "application/json");

            // Act
            var _client = _factory.CreateClient();
            _client.DefaultRequestHeaders.Add("userName", "IntegrationTest");
            var createResponse = await _client.PostAsync(PATH, content);
            createResponse.EnsureSuccessStatusCode();

            var createdProjectContent = await createResponse.Content.ReadAsStringAsync();
            var options = new JsonSerializerOptions(JsonSerializerDefaults.Web);
            var createdRetailerSeparation = JsonSerializer.Deserialize<RetailerSeperationResponse>(createdProjectContent, options);


            var getResponse = await _client.GetAsync(PATH + "/" + createdRetailerSeparation.Id);
            var getResponseContent = await getResponse.Content.ReadAsStringAsync();

            var result = JsonSerializer.Deserialize<RetailerSeperationRequest>(getResponseContent, options);

            // Assert
            getResponse.StatusCode.Should().Be(HttpStatusCode.OK);
            getResponseContent.Should().NotBeNull();
            result.Should().NotBeNull();
            result.Should().BeOfType<RetailerSeperationRequest>();
        }

        [Fact]
        public async Task GetAsync_RetailerSeperations_Returns_NotFound_Response()
        {
            // Arrange
            var requestId = 1;

            // Act
            var _client = _factory.CreateClient();
            _client.DefaultRequestHeaders.Add("userName", "IntegrationTest");

            var getResponse = await _client.GetAsync(PATH + "/" + requestId);
            var getResponseContent = await getResponse.Content.ReadAsStringAsync();
            var options = new JsonSerializerOptions(JsonSerializerDefaults.Web);
            var result = JsonSerializer.Deserialize<RetailerSeperationRequest>(getResponseContent, options);

            // Assert
            getResponse.StatusCode.Should().Be(HttpStatusCode.NotFound);
            getResponseContent.Should().NotBeNull();
            result.Should().NotBeNull();
            result.Should().BeOfType<RetailerSeperationRequest>();
        }

        [Fact]
        public async Task DeleteAsyncSourceBP_Returns_MultiStatus_Response()
        {
            using (var scope = _factory.Services.CreateScope())
            {
                // Arrange
                var retailerSeperationRequestId = new Random().Next(0, 1000) + _fixture.Create<int>();
                var retailerSeperationId = new Random().Next(0, 1000) + _fixture.Create<int>();
                var dbContext = scope.ServiceProvider.GetRequiredService<PostgreSqlDbContext>();
                var baseProject = _fixture.Create<BaseProjectEntity>();
                baseProject.Id = new Random().Next(0, 1000) + _fixture.Create<int>();
                baseProject.PanelId = 1;
                baseProject.DataTypeId = baseProject.DataType.Id;
                baseProject.QCProjects.Id = new Random().Next(0, 1000) + _fixture.Create<int>();
                baseProject.QCProjects.ResetCorrectionTypeId = 1;
                baseProject.CountryId = 15;
                baseProject.Purpose.Id = new Random().Next(0, 1000) + _fixture.Create<int>();
                foreach (var predecessor in baseProject.Predecessors)
                {
                    predecessor.Id = new Random().Next(0, 1000) + _fixture.Create<int>();
                   
                }
                foreach (var productGroups in baseProject.ProductGroups)
                {
                    productGroups.Id = new Random().Next(0, 1000) + _fixture.Create<int>();

                }
                foreach (var qcPeriod in baseProject.QCProjects.QCPeriods)
                {
                    qcPeriod.Id = new Random().Next(0, 1000) + _fixture.Create<int>();
                    qcPeriod.StockInitialization.Id = new Random().Next(0, 1000) + _fixture.Create<int>();
                    foreach (var period in qcPeriod.Periods)
                    {
                        period.Id = new Random().Next(0, 1000) + _fixture.Create<int>();
                    }
                }

                var retailerSeperationRequest = new RetailerSeperationRequestEntity
                {
                    Id = retailerSeperationRequestId,
                    JiraId = "STS-123456",
                    FromPeriodId = 0,
                    ToPeriodId = 0,
                    Extrapolation = false,
                    ResetCorrection = false,
                    RequestStatusId = 1,
                    RetailerSeperations = new List<RetailerSeperationEntity>
                     {
                         new RetailerSeperationEntity
                         {
                             Id= retailerSeperationId,
                             SourceBPId = baseProject.Id,
                             RetailerBPId = 0,
                             RetailerSeperationRequestId = retailerSeperationRequestId,
                             IsError = false
                         }
                     },
                                RetailerSeperationRequestDetails = new List<RetailerSeperationRequestDetailEntity>
                     {
                         new RetailerSeperationRequestDetailEntity
                         {
                             RequestStatusId = 1,
                             RetailerSeperationRequestId = retailerSeperationRequestId,
                             UpdatedBy = "testUser",
                             UpdatedWhen = DateTimeOffset.Now
                         }
                     }
                };

                dbContext.BaseProjects.Add(baseProject);
                dbContext.RetailerSeperationRequests.Add(retailerSeperationRequest);

                await dbContext.SaveChangesAsync();

                // Arrange
                var client = _factory.CreateClient();
                client.DefaultRequestHeaders.Add("userName", "IntegrationTest");

                var deleteRequest = new IRSeparationSourceBaseprojectsDeleteRequest
                {
                    RetailerSeperationIds = new List<int> { retailerSeperationId }
                };

                var stringContent = new StringContent(
                    JsonSerializer.Serialize(deleteRequest),
                    Encoding.UTF8,
                    "application/json"
                );

                // Act
                var request = new HttpRequestMessage(HttpMethod.Delete, PATH);

                request.Content = stringContent;

                var response = await client.SendAsync(request);

                // Assert
                response.StatusCode.Should().Be(HttpStatusCode.MultiStatus);
                response.Content.Headers.ContentType?.MediaType.Should().Be("application/json");

                var responseContent = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions(JsonSerializerDefaults.Web);
                var responseObject = JsonSerializer.Deserialize<List<ResponseInfoRetailerSeperation>>(responseContent, options);

                responseObject.Should().NotBeNull();
                responseObject.Count.Should().BeGreaterThan(0);
            }
        }

        [Fact]
        public async Task DeleteAsyncSourceBP_NoAuthorizedCountries_ReturnsForbidden()
        {
            using (var scope = _factory.Services.CreateScope())
            {
                // Arrange
                var retailerSeperationRequestId = new Random().Next(1000, 10000) + _fixture.Create<int>();
                var retailerSeperationId = new Random().Next(0, 1000) + _fixture.Create<int>();
                var dbContext = scope.ServiceProvider.GetRequiredService<PostgreSqlDbContext>();
                var baseProject = _fixture.Create<BaseProjectEntity>();
                baseProject.Id = new Random().Next(0, 1000) + _fixture.Create<int>();
                baseProject.PanelId = 1;
                baseProject.DataTypeId = baseProject.DataType.Id;
                baseProject.QCProjects.Id = new Random().Next(0, 1000) + _fixture.Create<int>();
                baseProject.QCProjects.ResetCorrectionTypeId = 1;
                baseProject.CountryId = 15;
                baseProject.Purpose.Id = new Random().Next(0, 1000) + _fixture.Create<int>();
                foreach (var predecessor in baseProject.Predecessors)
                {
                    predecessor.Id = new Random().Next(0, 1000) + _fixture.Create<int>();

                }
                foreach (var productGroups in baseProject.ProductGroups)
                {
                    productGroups.Id = new Random().Next(0, 1000) + _fixture.Create<int>();

                }
                foreach (var qcPeriod in baseProject.QCProjects.QCPeriods)
                {
                    qcPeriod.Id = new Random().Next(0, 1000) + _fixture.Create<int>();
                    qcPeriod.StockInitialization.Id = new Random().Next(0, 1000) + _fixture.Create<int>();
                    foreach (var period in qcPeriod.Periods)
                    {
                        period.Id = new Random().Next(0, 1000) + _fixture.Create<int>();
                    }
                }

                var retailerSeperationRequest = new RetailerSeperationRequestEntity
                {
                    Id = retailerSeperationRequestId,
                    JiraId = "STS-123456",
                    FromPeriodId = 0,
                    ToPeriodId = 0,
                    Extrapolation = false,
                    ResetCorrection = false,
                    RequestStatusId = 1,
                    RetailerSeperations = new List<RetailerSeperationEntity>
                     {
                         new RetailerSeperationEntity
                         {
                             Id= retailerSeperationId,
                             SourceBPId = baseProject.Id,
                             RetailerBPId = 0,
                             RetailerSeperationRequestId = retailerSeperationRequestId,
                             IsError = false
                         }
                     },
                    RetailerSeperationRequestDetails = new List<RetailerSeperationRequestDetailEntity>
                     {
                         new RetailerSeperationRequestDetailEntity
                         {
                             RequestStatusId = 1,
                             RetailerSeperationRequestId = retailerSeperationRequestId,
                             UpdatedBy = "testUser",
                             UpdatedWhen = DateTimeOffset.Now
                         }
                     }
                };

                dbContext.BaseProjects.Add(baseProject);
                dbContext.RetailerSeperationRequests.Add(retailerSeperationRequest);

                await dbContext.SaveChangesAsync();

                // Arrange
                var client = _factory.CreateClient();
                client.DefaultRequestHeaders.Add("userName", "IntegrationTest");
                client.DefaultRequestHeaders.Add("Custom-Countryid", "18");

                var deleteRequest = new IRSeparationSourceBaseprojectsDeleteRequest
                {
                    RetailerSeperationIds = new List<int> { retailerSeperationId }
                };

                var stringContent = new StringContent(
                    JsonSerializer.Serialize(deleteRequest),
                    Encoding.UTF8,
                    "application/json"
                );

                // Act
                var request = new HttpRequestMessage(HttpMethod.Delete, PATH);

                request.Content = stringContent;

                var response = await client.SendAsync(request);

                // Assert
                response.StatusCode.Should().Be(System.Net.HttpStatusCode.Forbidden);
            }
        }


        [Fact]
        public async Task UpdateStatusAsync_ReturnsNotFound_WhenRequestIdNotFound()
        {
            // Arrange
            var request = new RetailerSeperationStatusRequest
            {
                retailerSeperationRequestIds = new List<int> { 99999 }, 
                statusId = 1,
                reason = "Test Reason"
            };
            var content = new StringContent(JsonSerializer.Serialize(request), Encoding.UTF8, "application/json");

            // Act
            var _client = _factory.CreateClient();
            _client.DefaultRequestHeaders.Add("userName", "IntegrationTest");
            var updateResponse = await _client.PutAsync(PATH + "/status", content);

            // Assert
            updateResponse.StatusCode.Should().Be(HttpStatusCode.NotFound);
            var responseBody = await updateResponse.Content.ReadAsStringAsync();
            var problemDetails = JsonSerializer.Deserialize<ProblemDetails>(responseBody);
            problemDetails.Title.Should().Be("Provided request id 99999 not found.");
        }

        [Fact]
        public async Task UpdateStatusAsync_ReturnsBadRequest_WhenNoRequestIdsProvided()
        {
            // Arrange
            var request = new RetailerSeperationStatusRequest { retailerSeperationRequestIds = null };
            var content = new StringContent(JsonSerializer.Serialize(request), Encoding.UTF8, "application/json");

            // Act
            var _client = _factory.CreateClient();
            _client.DefaultRequestHeaders.Add("userName", "IntegrationTest");
            var updateResponse = await _client.PutAsync(PATH + "/status", content);

            // Assert
            updateResponse.StatusCode.Should().Be(HttpStatusCode.BadRequest);
            var responseBody = await updateResponse.Content.ReadAsStringAsync();
            var problemDetails = JsonSerializer.Deserialize<ProblemDetails>(responseBody);
            problemDetails.Title.Should().Be("No Retailer Separation Request IDs provided.");
        }

        [Fact]
        public async Task UpdateStatusAsync_ValidRequest_ReturnsMultiStatus()
        {
            using (var scope = _factory.Services.CreateScope())
            {
                // Arrange
                var qcPeriodId = new Random().Next(0, 1000) + _fixture.Create<int>();
                var retailerSeperationRequestId = new Random().Next(0, 1000) + _fixture.Create<int>();
                var dbContext = scope.ServiceProvider.GetRequiredService<PostgreSqlDbContext>();
                var baseProject = _fixture.Create<BaseProjectEntity>();
                baseProject.PanelId = 1;
                baseProject.DataTypeId = baseProject.DataType.Id;
                baseProject.QCProjects.Id = new Random().Next(0, 1000) + _fixture.Create<int>();
                baseProject.QCProjects.ResetCorrectionTypeId = 1;
                baseProject.QCProjects.QCPeriods.Add(
                    new QCPeriodEntity
                    {
                        Id = qcPeriodId,
                        QCProjectId = baseProject.QCProjects.Id,
                        CreatedBy = _fixture.Create<string>(),
                        CreatedWhen = _fixture.Create<DateTimeOffset>(),
                        UpdatedBy = _fixture.Create<string>(),
                        UpdatedWhen = _fixture.Create<DateTimeOffset>(),
                        Periods = new List<PeriodEntity>
                        {
                            new PeriodEntity
                            {
                                Id = new Random().Next(0, 1000) + _fixture.Create<int>(),
                                index = 1,
                                QCPeriodId = qcPeriodId,
                                RefPeriodId = 1,
                                RefProjectId = 1
                            }
                        }
                    });

                var retailerSeperationRequest = new RetailerSeperationRequestEntity
                {
                    Id = retailerSeperationRequestId,
                    JiraId = "STS-123456",
                    FromPeriodId = 0,
                    ToPeriodId = 0,
                    Extrapolation = false,
                    ResetCorrection = false,
                    RequestStatusId = 1,
                    RetailerSeperations = new List<RetailerSeperationEntity>
                    {
                        new RetailerSeperationEntity
                        {
                            SourceBPId = baseProject.Id,
                            RetailerBPId = 0,
                            RetailerSeperationRequestId = retailerSeperationRequestId,
                            IsError = false
                        }
                    },
                    RetailerSeperationRequestDetails = new List<RetailerSeperationRequestDetailEntity>
                    {
                        new RetailerSeperationRequestDetailEntity
                        {
                            Id = new Random().Next(0, 1000) + _fixture.Create<int>(),
                            RequestStatusId = 1,
                            RetailerSeperationRequestId = retailerSeperationRequestId,
                            UpdatedBy = "testUser",
                            UpdatedWhen = DateTimeOffset.Now
                        }
                    }
                };

                dbContext.BaseProjects.Add(baseProject);
                dbContext.RetailerSeperationRequests.Add(retailerSeperationRequest);

                await dbContext.SaveChangesAsync();

                _fixture.Customize<RetailerSeperationCreateRequest>(c => c
                    .With(p => p.RequestStatusId, 1)
                    .With(p => p.RetailerSeperations, new List<RetailerSeperationModelDto>() {
                            new RetailerSeperationModelDto
                            {
                                SourceBPId = baseProject.Id
                            }
                    }));
                _fixture.Customize<RetailerSeperationRequestDetailModelDto>(c => c
                   .With(p => p.RequestStatusId, 1));

                var request = _fixture.Create<RetailerSeperationCreateRequest>();

                var content = new StringContent(JsonSerializer.Serialize(request), Encoding.UTF8, "application/json");

                // Act
                var _client = _factory.CreateClient();
                _client.DefaultRequestHeaders.Add("userName", "IntegrationTest");
                var createResponse = await _client.PostAsync(PATH, content);
                createResponse.EnsureSuccessStatusCode();

                var createdProjectContent = await createResponse.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions(JsonSerializerDefaults.Web);
                var createdRetailerSeparation = JsonSerializer.Deserialize<RetailerSeperationResponse>(createdProjectContent, options);

                var statusRequest = new RetailerSeperationStatusRequest
                {
                    retailerSeperationRequestIds = new List<int>
                    {
                        12345,
                        13456,
                        35345
                    },
                    statusId = 3,
                    reason = string.Empty,
                };

                // Add headers and send the update request
                _client.DefaultRequestHeaders.Add("Custom-Countryid", baseProject.CountryId.ToString());

                var response = await _client.PutAsJsonAsync(PATH + "/status" + createdRetailerSeparation.Id, statusRequest);

                var responseContent = await response.Content.ReadAsStringAsync();
                var result = JsonSerializer.Deserialize<BaseProjectEditResponse>(responseContent, options);

                response.StatusCode.Should().Be(HttpStatusCode.OK);

                responseContent.Should().NotBeNull();
                result.Should().NotBeNull();
            }
        }

        [Fact]
        public async Task AddAsync_QCStatusBP_ReturnsBadRequest()
        {
            using (var scope = _factory.Services.CreateScope())
            {
                // Arrange
                var dbContext = scope.ServiceProvider.GetRequiredService<OracleDbContext>();
                var qcStatusBP = await dbContext.QCStatus.Where(x=>x.Deleted==0 && x.Status==6).Select(i => i.BaseProjectId).FirstOrDefaultAsync();

                RetailerSeperationCreateRequest request = new RetailerSeperationCreateRequest()
                {
                    FromPeriodId = 123,
                    ToPeriodId = 124,
                    ResetCorrection = true,
                    Extrapolation = false,
                    RetailerSeperations = new List<RetailerSeperationModelDto>(){
                        new RetailerSeperationModelDto()
                        {
                            SourceBPId= qcStatusBP
                        } 
                    }
                };

                var content = new StringContent(JsonSerializer.Serialize(request), Encoding.UTF8, "application/json");

                // Act
                var _client = _factory.CreateClient();
                _client.DefaultRequestHeaders.Add("userName", "IntegrationTest");
                var response = await _client.PostAsync(PATH, content);

                // Assert
                response.StatusCode.Should().Be(System.Net.HttpStatusCode.BadRequest);
                var responseContent = await response.Content.ReadAsStringAsync();
                responseContent.Should().NotBeNullOrEmpty();
                responseContent.Should().Contain("Selected BPs have at least one QC Period in QC Status. ("+ qcStatusBP + ")");


            }

        }

        [Fact]
        public async Task CreateAsyncIRSeparationBaseProject_ValidRequest_ReturnsMultiStatus()

        {
            using (var scope = _factory.Services.CreateScope())
            {

                var _client = _factory.CreateClient();
                //Create IR BaseProject First
                var IRBaseProjectId = await CreateBaseProjectIR();

                // Arrange
                var dbContext = scope.ServiceProvider.GetRequiredService<PostgreSqlDbContext>();
                var IRRequestId = await AddAsyncIRRequest(IRBaseProjectId);


                var initialBPCount = await dbContext.BaseProjects.CountAsync();
                var initialOutboxCount = await dbContext.OutBoxItem.CountAsync();
                var initialRetailerCount = await dbContext.BaseProjects.Where(x => x.TypeId == 2).CountAsync();
                var initiallIndustryCount = await dbContext.BaseProjects.Where(x => x.TypeId == 1).CountAsync();
                var initialIndustryRetailerCount = await dbContext.BaseProjects.Where(x => x.TypeId == 3).CountAsync();
                var userName = "testUser";
                var irSeparationRequest = new IRSeparationBaseProjectRequest { retailerSeparationId = IRRequestId,
                    SourceBaseProjectId = IRBaseProjectId,
                };

                // Act
                _client.DefaultRequestHeaders.Add("userName", userName);
                var response = await _client.PostAsync(
                    PATH + "/IRSeparationBaseProject",
                    new StringContent(JsonSerializer.Serialize(irSeparationRequest), Encoding.UTF8, "application/json")
                );
                var finalBPCount = await dbContext.BaseProjects.CountAsync();
                var finalRetailerCount = await dbContext.BaseProjects.Where(x => x.TypeId == 2).CountAsync();
                var finalIndustryCount = await dbContext.BaseProjects.Where(x => x.TypeId == 1).CountAsync();
                var finalIndustryRetailerCount = await dbContext.BaseProjects.Where(x => x.TypeId == 3).CountAsync();


                // Assertions
                response.IsSuccessStatusCode.Should().BeTrue();
                response.StatusCode.Should().Be(HttpStatusCode.OK); 

                //Check that one IR is decreased as its type changed, one Industry and Retailer increased
                finalBPCount.Should().Be(initialBPCount + 1);
                finalRetailerCount.Should().Be(initialRetailerCount + 1);
                finalIndustryCount.Should().Be(initiallIndustryCount + 1);
                finalIndustryRetailerCount.Should().Be(initialIndustryRetailerCount - 1);
                var finalOutboxCount = await dbContext.OutBoxItem.CountAsync();
                finalOutboxCount.Should().Be(initialOutboxCount +7);

            }
        }

        private async Task<int> CreateBaseProjectIR()
        {
            using (var scope = _factory.Services.CreateScope())
            {

                var _client = _factory.CreateClient();
                _client.DefaultRequestHeaders.Add("userName", "IntegrationTest");


                var request = new BaseProjectCreateRequest
                {
                    Name = "CL_Toys INFANT & PRESCH 1w",
                    TypeId = 3,
                    PanelId = 1,
                    DataTypeId = 1,
                    PurposeId = 1,
                    PeriodicityId = 2,
                    CountryId = 47,
                    ProductGroups = new List<BaseProjectProductGroupModelDto>
                    {
                        new BaseProjectProductGroupModelDto { ProductGroupId = 471384 }
                    },
                    Predecessors = new List<BaseProjectPredecessorModelDto>
                    {
                        new BaseProjectPredecessorModelDto { PredecessorId = 223873 } // Ensure valid Predecessor
                    },
                    IsRelevantForReportingEnabled = true,
                    ResetCorrectionTypeId = 1,
                    IsAutoLoad = false,
                    SQCMode = 0,
                    IsAutomatedPriceCheck = false
                };

                var content = new StringContent(JsonSerializer.Serialize(request), Encoding.UTF8, "application/json");

                // ✅ Act - Send API Request
                var response = await _client.PostAsync("/api/v1/baseprojects", content);
                var responseContent = await response.Content.ReadAsStringAsync();
                var options = new JsonSerializerOptions(JsonSerializerDefaults.Web);
                var result = JsonSerializer.Deserialize<BaseProject>(responseContent, options);
                return result.Id;
            }
        }

        private async Task<int> AddAsyncIRRequest(int sourceBPId)
        {
            // Arrange
            var _client = _factory.CreateClient();

            var request = new RetailerSeperationCreateRequest
            {
                FromPeriodId = 0,
                ToPeriodId = 0,
                ResetCorrection = true,
                Extrapolation = false,
                RetailerSeperations = new List<RetailerSeperationModelDto>
                {
                    new RetailerSeperationModelDto
                    {
                        SourceBPId = sourceBPId
                    }
                },
                RetailerSeperationRequestDetails = new List<RetailerSeperationRequestDetailModelDto>
                {
                    new RetailerSeperationRequestDetailModelDto
                    {
                        UpdatedWhen = DateTimeOffset.UtcNow,
                        RequestStatusId = 1,
                        UpdatedBy = "admin"
                    }
                },
                    RequestStatusId = 1
                };

            var content = new StringContent(JsonSerializer.Serialize(request), Encoding.UTF8, "application/json");
            _client.DefaultRequestHeaders.Add("userName", "testUser");

            // Act
            var response = await _client.PostAsync(PATH, content);
            var createdRequest = await response.Content.ReadFromJsonAsync<RetailerSeperationResponse>(); // Assuming BaseProjectResponse with QCPeriods info
            return createdRequest.Id;
        }

    }
}
