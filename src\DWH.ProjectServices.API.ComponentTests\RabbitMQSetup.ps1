param(
    [string]$RabbitMQHost = "http://localhost:15672",
    [string]$Username = "guest",
    [string]$Password = "guest",
    [string]$VHost = "vhbdxt1",
    [string]$QueueName = "project.services-app",
    [string]$RetailerSeparationQueue = "retailer.separation-app",
    [string]$ProjectServicesSecurityQueue = "project.services.security-app",
    [string]$UserRoleQueue = "user.role.assignment-app"
)

# Generate Base64 Authorization Header
$base64AuthInfo = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes("$Username`:$Password"))
$headers = @{ Authorization = "Basic $base64AuthInfo" }
$LimitedRoutingExchanges = @("bpsecurity_exchange", "qcsecurity_exchange")
$FullRoutingExchanges = @("baseProject_exchange", "qcperiod_exchange", "qcproject_exchange")
$RetailerSeparationExchanges = @("retailerseparation_exchange")
$ProjectServicesSecurityExchanges = @("projectservicessecurity_exchange")
$UserRoleExchanges = @("userRole_exchange")

# Function to create a virtual host
# function Create-VHost {
#     param ([string]$VHost)
#     Invoke-RestMethod -Uri "$RabbitMQHost/api/vhosts/$VHost" -Method Put -Headers $headers
# }

# Function to create a queue
function Create-Queue {
    param ([string]$VHost, [string]$QueueName)
    $body = @{ durable = $true } | ConvertTo-Json -Depth 10
    Invoke-RestMethod -Uri "$RabbitMQHost/api/queues/$VHost/$QueueName" -Method Put -Headers $headers -Body $body -ContentType "application/json"
}

# Function to create an exchange
function Create-Exchange {
    param ([string]$VHost, [string]$ExchangeName, [string]$Type = "direct")
    $body = @{ type = $Type; durable = $true } | ConvertTo-Json -Depth 10
    Invoke-RestMethod -Uri "$RabbitMQHost/api/exchanges/$VHost/$ExchangeName" -Method Put -Headers $headers -Body $body -ContentType "application/json"
}

# Function to create a binding between an exchange and a queue
function Create-Binding {
    param ([string]$VHost, [string]$ExchangeName, [string]$QueueName, [string]$RoutingKey)
    $body = @{ routing_key = $RoutingKey } | ConvertTo-Json -Depth 10
    Invoke-RestMethod -Uri "$RabbitMQHost/api/bindings/$VHost/e/$ExchangeName/q/$QueueName" -Method Post -Headers $headers -Body $body -ContentType "application/json"
}

# Execution
#Create-VHost -VHost $VHost
Create-Queue -VHost $VHost -QueueName $QueueName

# Create exchanges with full routing keys
foreach ($Exchange in $FullRoutingExchanges) {
    Create-Exchange -VHost $VHost -ExchangeName $Exchange -Type "direct"
    foreach ($RoutingKey in @("create", "update", "delete")) {
        Create-Binding -VHost $VHost -ExchangeName $Exchange -QueueName $QueueName -RoutingKey $RoutingKey
    }
}

# Create exchanges with limited routing keys (excluding "update")
foreach ($Exchange in $LimitedRoutingExchanges) {
    Create-Exchange -VHost $VHost -ExchangeName $Exchange -Type "direct"
    foreach ($RoutingKey in @("create", "delete")) {
        Create-Binding -VHost $VHost -ExchangeName $Exchange -QueueName $QueueName -RoutingKey $RoutingKey
    }
}

# Create retailer separation queue ,security and role
Create-Queue -VHost $VHost -QueueName $RetailerSeparationQueue
Create-Queue -VHost $VHost -QueueName $ProjectServicesSecurityQueue
Create-Queue -VHost $VHost -QueueName $UserRoleQueue

# Create retailer separation exchange bindings (only "create" routing key)
foreach ($Exchange in $ProjectServicesSecurityExchanges) {
    Create-Exchange -VHost $VHost -ExchangeName $Exchange -Type "direct"
    Create-Binding -VHost $VHost -ExchangeName $Exchange -QueueName $ProjectServicesSecurityQueue -RoutingKey "update"
}

# Create retailer separation exchange bindings (only "create" routing key)
foreach ($Exchange in $RetailerSeparationExchanges) {
    Create-Exchange -VHost $VHost -ExchangeName $Exchange -Type "direct"
    Create-Binding -VHost $VHost -ExchangeName $Exchange -QueueName $RetailerSeparationQueue -RoutingKey "create"
}

# Create retailer separation exchange bindings (only "update" routing key)
foreach ($Exchange in $RetailerSeparationExchanges) {
    Create-Exchange -VHost $VHost -ExchangeName $Exchange -Type "direct"
    Create-Binding -VHost $VHost -ExchangeName $Exchange -QueueName $QueueName -RoutingKey "update"
}

# Create userrole exchange bindings (only "create and delete" routing key)
foreach ($Exchange in $UserRoleExchanges) {
    Create-Exchange -VHost $VHost -ExchangeName $Exchange -Type "direct"
     foreach ($RoutingKey in @("create", "delete")) {
        Create-Binding -VHost $VHost -ExchangeName $Exchange -QueueName $UserRoleQueue -RoutingKey $RoutingKey
    }
}

<# 
Example Execution:
    powershell -ExecutionPolicy Bypass -File .\RabbitMQSetup.ps1 -RabbitMQHost "http://localhost:15672" -Username "guest" -Password "guest" -VHost "vhbdxt1"
    powershell -ExecutionPolicy Bypass -File .\RabbitMQSetup.ps1 -RabbitMQHost "https://rabbitmq.de.shared.in.gfk.com" -Username "bdxadm" -Password "LwecvYo0hlRTcjxZhHQ3" -VHost "vhbdxt"
    powershell -ExecutionPolicy Bypass -File .\RabbitMQSetup.ps1 -RabbitMQHost "https://rabbitmq.de.vpc1445.in.gfk.com" -Username "bdxadmt1" -Password "8PiNaQN6Saq4dDg2Hw" -VHost "vhbdxt1"
#>
