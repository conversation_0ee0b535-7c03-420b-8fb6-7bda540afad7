﻿using System;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using DWH.ProjectServices.API.Middleware;
using DWH.ProjectServices.API.Models.Dtos;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using Xunit;
using FluentAssertions;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;

namespace DWH.ProjectServices.API.UnitTests.Middleware
{
    public class CountryFilterTests
    {
        [Fact]
        public async Task InvokeAsync_WithValidCountryHeader_ShouldFilterCountryIds()
        {
            // Arrange
            var countryIds = "1,2,3";
            var baseProjectListRequest = new BaseProjectListRequest { CountryIds = new int[] { 1, 2, 4, 5 } };
            var bodyStr = JsonSerializer.Serialize(baseProjectListRequest);
            var bodyBytes = Encoding.UTF8.GetBytes(bodyStr);

            var context = new DefaultHttpContext();
            context.Request.Headers["Custom-Countryid"] = countryIds;
            context.Request.Body = new MemoryStream(bodyBytes);
            context.Request.ContentLength = bodyBytes.Length;
            context.Response.Body = new MemoryStream();

            var nextMock = new Mock<RequestDelegate>();

            var middleware = new CountryFilter(nextMock.Object);

            // Act
            await middleware.InvokeAsync(context);

            // Assert
            context.Items["CountryIds"].Should().BeEquivalentTo(new string[] { "1", "2", "3" });
            var filteredRequest = context.Items["FilteredBaseProjectListRequest"] as BaseProjectListRequest;
            filteredRequest.Should().NotBeNull();
            filteredRequest.CountryIds.Should().BeEquivalentTo(new int[] { 1, 2 });
            nextMock.Verify(m => m(context), Times.Once);
        }

        [Fact]
        public async Task InvokeAsync_WithoutCountryHeader_ShouldSetItemsToNull()
        {
            // Arrange
            var context = new DefaultHttpContext();
            context.Request.Body = new MemoryStream();
            context.Response.Body = new MemoryStream();

            var nextMock = new Mock<RequestDelegate>();

            var middleware = new CountryFilter(nextMock.Object);

            // Act
            await middleware.InvokeAsync(context);

            // Assert
            context.Items["CountryIds"].Should().BeNull();
            context.Items["FilteredBaseProjectListRequest"].Should().BeNull();
            nextMock.Verify(m => m(context), Times.Once);
        }
    }
}
