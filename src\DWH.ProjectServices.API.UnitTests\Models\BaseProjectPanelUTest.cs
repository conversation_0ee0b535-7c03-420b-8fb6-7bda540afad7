﻿using AutoFixture;
using DWH.ProjectServices.API.Infrastructure.Persistence.Entities;
using FluentAssertions;
using Xunit;

namespace DWH.ProjectServices.API.UnitTests.Models
{
    public class BaseProjectPanelTests
    {
        private readonly IFixture _fixture;

        public BaseProjectPanelTests()
        {
            _fixture = new Fixture();
        }

        [Fact]
        public void When_ValidParameters_Expect_NotNullInstance()
        {
            // Arrange
            var id = _fixture.Create<int>();
            var name = _fixture.Create<string>();

            // Act
            var instance = new BaseProjectPanelEntity
            {
                Id = id,
                Name = name
            };

            // Assert
            instance.Should().NotBeNull();
            instance.Id.Should().Be(id);
            instance.Name.Should().Be(name);
        }
    }
}
