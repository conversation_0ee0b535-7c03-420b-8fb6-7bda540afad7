﻿using DWH.ProjectServices.API.Infrastructure.Persistence.Entities;
using FluentAssertions;
using System;
using System.ComponentModel.DataAnnotations.Schema;
using Xunit;

namespace DWH.ProjectServices.API.UnitTests.Models
{
    public class BaseProjectPredecessorTests
    {
        [Fact]
        public void When_ValidParameters_Expect_NotNullInstance()
        {
            // Arrange
            var instance = new BaseProjectPredecessorEntity
            {
                Id = 1,
                BaseProjectId = 2,
                PredecessorId=3
            };

            // Act & Assert
            instance.Should().NotBeNull(); 

        }
    }
}
