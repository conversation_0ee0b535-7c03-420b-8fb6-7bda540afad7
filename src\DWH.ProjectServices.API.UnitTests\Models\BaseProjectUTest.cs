﻿using DWH.ProjectServices.API.Infrastructure.Persistence.Entities;
using FluentAssertions;
using Xunit;

namespace DWH.ProjectServices.API.UnitTests.Models
{
    public class BaseProjectUTest
    {
        [Fact]
        public void When_ValidParameters_Expect_NotNullInstance()
        {
            // Arrange
            var instance = new BaseProjectEntity
            {
                Id = 1,
                Name = "Test Project",
                TypeId = 2,
                PanelId = 3,
                DataTypeId = 4,
                PurposeId = 5,
                PeriodicityId = 6,
                CountryId = 7,
                IsRelevantForReportingEnabled = true,
                CreatedBy = "User",
                CreatedWhen = DateTime.UtcNow,
                UpdatedBy = "User",
                UpdatedWhen = DateTime.UtcNow.AddDays(1),
                DeletedBy = null,
                DeletedWhen = null,
                Deleted = false,
                ProductGroups = new List<BaseProjectProductGroupEntity>(),
                Predecessors = new List<BaseProjectPredecessorEntity>(),
                QCProjects = new QCProjectEntity(),
                DataType = new BaseProjectDataType(),
                Purpose = new BaseProjectPurpose()
            };

            // Act & Assert
            instance.Should().NotBeNull();
        }
    }
}
