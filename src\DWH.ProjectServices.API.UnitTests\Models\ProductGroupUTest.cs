﻿using AutoFixture;
using DWH.ProjectServices.API.Models;
using DWH.ProjectServices.API.Models.Dtos;
using FluentAssertions;

namespace DWH.ProjectServices.API.UnitTests.Models
{
    public class ProductGroupTests
    {
        private readonly IFixture _fixture;

        public ProductGroupTests()
        {
            _fixture = new Fixture();
        }

        [Fact]
        public void When_ValidParameters_Expect_NotNullInstance()
        {
            // Arrange
            var productGroupId = _fixture.Create<int>();
            var productGroupDesc = _fixture.Create<string>();

            // Act
            var instance = new ProductGroupResponse(productGroupId, productGroupDesc);

            // Assert
            instance.Should().NotBeNull();
        }
    }
}
