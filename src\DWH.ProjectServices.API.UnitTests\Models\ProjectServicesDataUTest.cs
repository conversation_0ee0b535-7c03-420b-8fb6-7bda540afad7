﻿using DWH.ProjectServices.API.Models;
using FluentAssertions;

namespace RabbitMQ.Tests
{
    public class ProjectServicesDataTests
    {
        [Fact]
        public void When_ValidParameters_Expect_NotNullInstance()
        {
            // Arrange
            var instance = new ProjectServicesData
            {
                Id = Guid.NewGuid(),
                SyncingEntityId = 12
            
            };

            // Act & Assert
            instance.Should().NotBeNull();
        }
    }
}
