﻿using AutoFixture;
using DWH.ProjectServices.API.Models;
using FluentAssertions;
using Npgsql;
using Xunit;
using DWH.ProjectServices.API.Infrastructure.Persistence.Entities;

namespace DWH.ProjectServices.API.UnitTests.Models
{
    public class QCProjectTests
    {
        private readonly IFixture _fixture;

        public QCProjectTests()
        {
            _fixture = new Fixture();
        }

        [Fact]
        public void When_ValidParameters_Expect_NotNullInstance()
        {
            // Arrange
            var id = _fixture.Create<int>();
            var baseProjectId = _fixture.Create<int>();
            var resetCorrectionTypeId = _fixture.Create<int?>();
            var isAutoLoad = _fixture.Create<bool>();
            var SQCMode = _fixture.Create<int>();
            var isAutomatedPriceCheck = _fixture.Create<bool>();
            var qcPeriods = _fixture.Create<ICollection<QCPeriodEntity>>();
            var resetCorrectionType = _fixture.Create<ResetCorrectionType>();

            // Act
            var instance = new QCProjectEntity
            {
                Id = id,
                BaseProjectId = baseProjectId,
                ResetCorrectionTypeId = resetCorrectionTypeId,
                IsAutoLoad = isAutoLoad,
                SQCMode = SQCMode,
                IsAutomatedPriceCheck = isAutomatedPriceCheck,
                QCPeriods = qcPeriods
            };

            // Assert
            instance.Should().NotBeNull();
            instance.Id.Should().Be(id);
            instance.BaseProjectId.Should().Be(baseProjectId);
            instance.ResetCorrectionTypeId.Should().Be(resetCorrectionTypeId);
            instance.IsAutoLoad.Should().Be(isAutoLoad);
            instance.SQCMode.Should().Be(SQCMode);
            instance.IsAutomatedPriceCheck.Should().Be(isAutomatedPriceCheck);
        }
    }
}
