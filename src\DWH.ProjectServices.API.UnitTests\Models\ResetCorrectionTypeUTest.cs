﻿using AutoFixture;
using DWH.ProjectServices.API.Models;
using FluentAssertions;

namespace DWH.ProjectServices.API.UnitTests.Models
{
    public class ResetCorrectionTypeUTests
    {
        private readonly IFixture _fixture;

        public ResetCorrectionTypeUTests()
        {
            _fixture = new Fixture();
        }

        [Fact]
        public void When_ValidParameters_Expect_NotNullInstance()
        {
            // Arrange
            var id = _fixture.Create<int>();
            var name = _fixture.Create<string>();

            // Act
            var instance = new ResetCorrectionType
            {
                Id = id,
                Name = name
            };

            // Assert
            instance.Should().NotBeNull();
            instance.Id.Should().Be(id);
            instance.Name.Should().Be(name);
        }
    }
}
