﻿using DWH.ProjectServices.API.Infrastructure.Persistence.Entities;
using DWH.ProjectServices.API.Models;
using FluentAssertions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DWH.ProjectServices.API.UnitTests.Models
{
    public class StockInitializationUTest
    {
        [Fact]
        public void When_ValidParameters_Expect_NotNullInstance()
        {
            // Arrange
            var instance = new StockInitializationEntity
            {
                Id = 1,
                StockBaseProjectId = 1,
                StockPeriodId = 1,
                QCPeriodId = 2
            };

            // Act & Assert
            instance.Should().NotBeNull();
        }
    }
}

