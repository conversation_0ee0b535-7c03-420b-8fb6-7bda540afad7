﻿using System.Collections.Generic;
using System.Threading.Tasks;
using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Infrastructure.Persistence.Entities;
using DWH.ProjectServices.API.Presentation.Controllers;
using DWH.ProjectServices.API.Services.Interfaces;
using FluentAssertions;
using Microsoft.AspNetCore.Mvc;
using Moq;
using Xunit;

namespace DWH.ProjectServices.API.UnitTests.Presentation.Controllers
{
    public class BaseProjectPurposeControllerTests
    {
        private readonly Mock<IBaseProjectPurposeService> _baseProjectPurposeServiceMock;
        private readonly BaseProjectPurposeController _controller;

        public BaseProjectPurposeControllerTests()
        {
            _baseProjectPurposeServiceMock = new Mock<IBaseProjectPurposeService>();
            _controller = new BaseProjectPurposeController(_baseProjectPurposeServiceMock.Object);
        }

        [Fact]
        public async Task GetAllAsync_When_CalledForNotEmptyResult_Expect_200Response()
        {
            // Arrange
            var expectedResult = new List<Purposes>
            {
                new Purposes(),
                new Purposes()
            };

            _baseProjectPurposeServiceMock
                .Setup(s => s.GetAllAsync())
                .ReturnsAsync(expectedResult);

            // Act
            var result = await _controller.GetAllAsync();

            // Assert
            result.Should().BeOfType<OkObjectResult>()
                .Which.Value.Should().BeEquivalentTo(expectedResult);
        }

        [Fact]
        public async Task GetAllAsync_When_ServiceReturnsNull_Expect_404NotFound()
        {
            // Arrange
            List<Purposes> expectedResult = null;

            _baseProjectPurposeServiceMock
                .Setup(s => s.GetAllAsync())
                .ReturnsAsync(expectedResult);

            // Act
            var result = await _controller.GetAllAsync();

            // Assert
            result.Should().BeOfType<NotFoundResult>();
        }
    }
}
