﻿using FluentAssertions;
using DWH.ProjectServices.API.Models;

using DWH.ProjectServices.API.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using Moq;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Xunit;
using DWH.ProjectServices.API.Models.Dtos;
using AutoFixture;
using DWH.ProjectServices.API.Presentation.Controllers;

namespace DWH.ProjectServices.API.UnitTests.Presentation.Controllers
{
    public class ProductGroupControllerTests
    {
        private readonly IFixture _fixture;
        private readonly Mock<IProductGroupService> _productGroupServiceMock;
        private readonly ProductGroupsController _controller;

        public ProductGroupControllerTests()
        {
            _fixture = new Fixture();
            _productGroupServiceMock = new Mock<IProductGroupService>();
            _controller = new ProductGroupsController(_productGroupServiceMock.Object);
        }

        [Fact]
        public async Task GetAsync_When_CalledForNotEmptyResult_Expect_200Response()
        {
            // Arrange
            var expectedResult = new List<ProductGroupResponse>
            {
                new ProductGroupResponse(1, "Group 1"),
                new ProductGroupResponse(2, "Group 2")
            };
            var productGroupRequestDto = _fixture.Create<ProductGroupRequest>();

            _productGroupServiceMock
                .Setup(s => s.GetAllAsync(productGroupRequestDto))
                .ReturnsAsync(expectedResult); // Return the tuple with expected result and null exception

            // Act
            var result = await _controller.GetAsync(productGroupRequestDto);

            // Assert
            result.Should().BeOfType<OkObjectResult>()
                .Which.Value.Should().BeEquivalentTo(expectedResult);

        }

        [Fact]
        public async Task GetProductSectorAsync_When_CalledForNotEmptyResult_Expect_200Response()
        {
            // Arrange
            var domainPgs = new List<DomainProductGroup>
            {
                new DomainProductGroup(321157, "TS MOBSW", "MediaEnt"),
                new DomainProductGroup(321167, "TSIMAGDO", "MediaEnt")
            };

            var expectedResult = new DescriptionResponse("1m", domainPgs);
            var descriptionDto = new DescriptionRequest(4, new int[] { 30738, 40021, 35611 });

            _productGroupServiceMock
                .Setup(s => s.GetDescriptionAsync(descriptionDto))
                .ReturnsAsync(expectedResult); // Return the tuple with expected result and null exception

            // Act
            var result = await _controller.GetAutoGenerateDescription(descriptionDto);

            // Assert
            result.Should().BeOfType<OkObjectResult>()
                .Which.Value.Should().BeEquivalentTo(expectedResult);

        }

        [Fact]
        public async Task GetAsync_When_ServiceReturnsNull_Expect_404NotFound()
        {
            // Arrange
            ProductGroupRequest productGroupRequestDto = new ProductGroupRequest();
            List<ProductGroupResponse> expectedResult = null;

            var productGroupServiceMock = new Mock<IProductGroupService>();
            productGroupServiceMock
                .Setup(s => s.GetAllAsync(productGroupRequestDto))
                .ReturnsAsync(expectedResult);

            var controller = new ProductGroupsController(productGroupServiceMock.Object);

            // Act
            var result = await controller.GetAsync(productGroupRequestDto);

            // Assert
            result.Should().BeOfType<NotFoundResult>();
        }

        [Fact]
        public async Task GetAutoGenerateDescription_When_ServiceReturnsNull_Expect_404NotFound()
        {
            // Arrange
            var periodicityId = 1;
            var productGroupIds = new[] { 100, 200 };
            var descriptionRequestDto = new DescriptionRequest(periodicityId, productGroupIds);
            DescriptionResponse expectedResult = null;

            var productGroupServiceMock = new Mock<IProductGroupService>();
            productGroupServiceMock
                .Setup(s => s.GetDescriptionAsync(descriptionRequestDto))
                .ReturnsAsync(expectedResult);

            var controller = new ProductGroupsController(productGroupServiceMock.Object);

            // Act
            var result = await controller.GetAutoGenerateDescription(descriptionRequestDto);

            // Assert
            result.Should().BeOfType<NotFoundResult>();
        }



    }
}
