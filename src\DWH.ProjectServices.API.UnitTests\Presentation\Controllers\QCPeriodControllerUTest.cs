﻿using AutoFixture;
using AutoMapper;
using BootstrapAPI.Core.Exception.Instances;
using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Infrastructure.Persistence.Entities;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ.Configuration;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ.Interfaces;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;
using DWH.ProjectServices.API.Presentation.Controllers;
using DWH.ProjectServices.API.Presentation.Profile;
using DWH.ProjectServices.API.Services.Interfaces;
using FluentAssertions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Moq;
using System.ComponentModel.DataAnnotations;
using System.Net;
using Xunit.Extensions.AssertExtensions;
using static DWH.ProjectServices.API.Domain.Models.BulkQCResponses;


namespace DWH.ProjectServices.API.UnitTests.Presentation.Controllers
{
    public class QCPeriodControllerUTest
    {
        private readonly IFixture _fixture;
        private readonly IMapper _mapper;
        private readonly Mock<IQCPeriodService> _qcPeriodServiceMock;
        private readonly Mock<IQCProjectService> _qcProjectServiceMock;
        private readonly QCPeriodsController _controller;

        public QCPeriodControllerUTest()
        {
            _fixture = new Fixture();
            var mockResponse = new Mock<HttpResponse>();
            var mockHttpContext = new Mock<HttpContext>();
            _qcPeriodServiceMock = new Mock<IQCPeriodService>();
            _qcProjectServiceMock = new Mock<IQCProjectService>();
            var rabbitMQSenderMock = new Mock<IRabbitMQSender>();
            var optionsMock = new Mock<IOptions<RabbitMQSettings>>();


            var mappingConfig = new MapperConfiguration(mc =>
            {
                mc.AddProfile(new BaseProjectProfile());
                mc.AddProfile(new QCProjectandPeriodProfile());
            });
            _mapper = mappingConfig.CreateMapper();

            _controller = new QCPeriodsController(_mapper, _qcPeriodServiceMock.Object, _qcProjectServiceMock.Object, rabbitMQSenderMock.Object);

            mockHttpContext.Setup(h => h.Response).Returns(mockResponse.Object);

        }

        [Fact]
        public async Task AddAsync_When_SuccessfullyAdded_Expect_201Response()
        {
            // Arrange
            var countryId = "15";
            var username = _fixture.Create<string>();
            var newqcPeriodDto = _fixture.Create<QCPeriodCreateRequest>();
            var qcPeriodResponse = _fixture.Create<QCPeriod>();
            var mockHttpContext = new Mock<HttpContext>();
            var mockResponse = new Mock<HttpResponse>();

            _controller.ControllerContext = new ControllerContext { HttpContext = mockHttpContext.Object };
            mockResponse.SetupGet(r => r.Headers).Returns(new HeaderDictionary());
            mockHttpContext.SetupGet(c => c.Response).Returns(mockResponse.Object);


            _qcPeriodServiceMock
                .Setup(s => s.AddAsyncQCPeriod(It.IsAny<QCPeriod>())).ReturnsAsync(qcPeriodResponse);
            _qcProjectServiceMock
                .Setup(s => s.GetAsync(It.IsAny<QCProjectCountries>()))
                .ReturnsAsync(new List<int> { newqcPeriodDto.QCProjectId });
            // Act
            var result = await _controller.AddAsync(countryId,username, newqcPeriodDto) as ObjectResult;

           
            // Assert
            result.Should().NotBeNull(); // Check that result is not null
            result.StatusCode.Should().Be(StatusCodes.Status201Created); // Check the status code

        }

        [Fact]
        public async Task AddAsync_When_ServiceReturnsNull_Expect_400Response()
        {
            // Arrange
            var countryId = "15";
            var username = _fixture.Create<string>();
            var newqcPeriodDto = _fixture.Create<QCPeriodCreateRequest>();

            _qcPeriodServiceMock
                .Setup(s => s.AddAsyncQCPeriod(It.IsAny<QCPeriod>()))
                .ReturnsAsync((QCPeriod)null);

            _qcProjectServiceMock
                .Setup(s => s.GetAsync(It.IsAny<QCProjectCountries>()))
                .ReturnsAsync(new List<int> { newqcPeriodDto.QCProjectId });

            // Act
            var result = await _controller.AddAsync(countryId, username, newqcPeriodDto) as ObjectResult;


            // Assert
            result.Should().NotBeNull(); // Check that result is not null
            result.StatusCode.Should().Be(StatusCodes.Status400BadRequest); // Check the status code

        }
        [Fact]
        public async Task GetQCPeriodAsync_When_Found_Expect_200Response()
        {
            // Arrange
            var qcPeriodId = _fixture.Create<long>();
            var qcPeriodDto = _fixture.Create<QCPeriodWithBPIdResponse>();

            _qcPeriodServiceMock
                .Setup(s => s.GetQCPeriodAsync(qcPeriodId))
                .ReturnsAsync(qcPeriodDto);

            // Act
            var result = await _controller.GetQCPeriodAsync(qcPeriodId) as ObjectResult;

    
            // Assert
            result.Should().NotBeNull(); // Check that result is not null
            result.StatusCode.Should().Be(StatusCodes.Status200OK); // Check the status code

        }



        [Fact]
        public async Task UpdateAsyncQCPeriod_When_SuccessfullyAdded_Expect_200Response()
        {
            // Arrange
            var username = _fixture.Create<string>();
            var qcEditRequest = _fixture.Create<QCPeriodEditRequest>();
            var qcPeriodEditResponse = _fixture.Create<QCPeriod>();
            var mockHttpContext = new Mock<HttpContext>();
            var mockResponse = new Mock<HttpResponse>();
            var periodId = 1;
            var countryIds = "15";

            _controller.ControllerContext = new ControllerContext { HttpContext = mockHttpContext.Object };
            mockResponse.SetupGet(r => r.Headers).Returns(new HeaderDictionary());
            mockHttpContext.SetupGet(c => c.Response).Returns(mockResponse.Object);


            _qcPeriodServiceMock
                .Setup(s => s.EditPeriodAsync(periodId, It.IsAny<QCPeriodEdits>())).ReturnsAsync(qcPeriodEditResponse);

            _qcPeriodServiceMock
                .Setup(s => s.GetAuthorizedCountriesForPeriods(It.IsAny<QCPeriodCountries>()))
                .ReturnsAsync(new List<long> { periodId });
            // Act
            var result = await _controller.UpdateAsyncQCPeriod(countryIds,username, periodId, qcEditRequest) as ObjectResult;

            // Assert
            result.Should().NotBeNull(); 
            result.StatusCode.Should().Be(StatusCodes.Status200OK); 
        }


        [Fact]
        public async Task GetQCAllPeriodsAsync_When_Called_Expect_200Response()
        {
            // Arrange
            var qcProjectId = 123;
            var qcPeriodResponse = _fixture.Create<IEnumerable<QCPeriod>>();
            _qcPeriodServiceMock
                .Setup(s => s.GetAllQCPeriodsAsync(qcProjectId)).ReturnsAsync(qcPeriodResponse);

            // Act
            var result = await _controller.GetAllQCPeriods(qcProjectId);

            // Assert
            result.Should().BeOfType<OkObjectResult>();
        }

        [Fact]
        public async Task GetQCPeriodAsync_When_Called_Expect_200Response()
        {
            // Arrange
            var qcPeriodId = 123;
            var qcPeriodResponse = _fixture.Create<QCPeriodWithBPIdResponse>();
            _qcPeriodServiceMock
                .Setup(s => s.GetQCPeriodAsync(qcPeriodId)).ReturnsAsync(qcPeriodResponse);

            // Act
            var result = await _controller.GetAllQCPeriods(qcPeriodId);

            // Assert
            result.Should().BeOfType<OkObjectResult>();
        }

        [Fact]
        public async Task DeleteAsyncQCPeriod_When_Called_Expect_207MultiStatusResponse()
        {
            // Arrange
            var countryId = "15";
            var qcPeriodDeleteRequest = new QCPeriodDeletes
            {
                Ids = new List<long> { 401, 402, 403 }
            };
            var qcPeriodDeletDTO = new QCPeriodDeleteRequest
            {
                Ids = new List<long> { 401, 402, 403 }
            };
            var responseList = new List<ResponseInfoQCPeriod>
    {
        new ResponseInfoQCPeriod("401", (int)HttpStatusCode.OK, HttpStatusCode.OK.ToString()),
        new ResponseInfoQCPeriod("402", (int)HttpStatusCode.NotFound, HttpStatusCode.NotFound.ToString()),
        new ResponseInfoQCPeriod("403", (int)HttpStatusCode.OK, HttpStatusCode.OK.ToString())
    };

            _qcPeriodServiceMock.Setup(s => s.DeletePeriodAsync(It.IsAny<QCPeriodDeletes>()))
                                 .ReturnsAsync(responseList);
            _qcPeriodServiceMock.Setup(s => s.GetAuthorizedCountriesForPeriods(It.IsAny<QCPeriodCountries>()))
                                 .ReturnsAsync( qcPeriodDeletDTO.Ids);

            // Act
            var result = await _controller.DeleteAsyncQCPeriod(countryId,qcPeriodDeletDTO) as ObjectResult;

            // Assert
            result.Should().NotBeNull();
            result.StatusCode.Should().Be(StatusCodes.Status207MultiStatus);
            result.Value.Should().BeEquivalentTo(responseList);
        }

        [Fact]
        public void QCPeriodDeleteRequest_When_IdsAreEmpty_ShouldReturnValidationResult()
        {
            // Arrange
            var qcPeriodDeleteRequest = new QCPeriodDeleteRequest
            {
                Ids = new List<long>()
            };

            // Act
            var validationResults = qcPeriodDeleteRequest.Validate(new ValidationContext(qcPeriodDeleteRequest)).ToList();

            // Assert
            validationResults.ShouldNotBeEmpty();
            validationResults.First().ErrorMessage.ShouldEqual("Must have atleast one id to proceed");
        }

        [Fact]
        public async Task AddAsync_When_ErrorAdded_Expect_400BadRequestWithProblemDetails()
        {
            try
            {
                // Arrange
                var countryId = "15";
                var username = _fixture.Create<string>();
                var newqcPeriodDto = _fixture.Create<QCPeriodCreateRequest>();

                _qcPeriodServiceMock
                    .Setup(s => s.AddAsyncQCPeriod(It.IsAny<QCPeriod>()))
                    .ThrowsAsync(new EntityAlreadyExistsException("A QCPeriod with the specified PeriodId already exists for this QCProjectId."));

                // Act
                var result = await _controller.AddAsync(countryId, username, newqcPeriodDto) as ObjectResult;

                // Assert
                result.ShouldNotBeNull();
                result.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
                result.Should().BeOfType<ProblemDetails>()
                    .Which.Status.Should().Be(StatusCodes.Status400BadRequest);

                result.Should().BeOfType<ProblemDetails>()
                    .Which.Title.Should().Be("DUPLICATION_ENTITY");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Exception occurred during assertions: {ex}");
            }

        }

        [Fact]
        public async Task AutoQCPeriodCreateAsync_When_SuccessfullyCreated_Expect_201Response()
        {
            // Arrange
            string username= "test_user";
            var targetPeriodId = 12345;
            var autoQCPeriodCreateRequest = new AutoQCPeriodCreateRequest
            {
                QCProjectIds = new List<int> { 1 }
            };
            var qcPeriodResponse = _fixture.Create<QCPeriod>();
            var autoQCPeriodResponse = _fixture.Create<AutoQCResponses>();
            _qcPeriodServiceMock
                .Setup(s => s.AutoQCPeriodCreateAsync(targetPeriodId, It.IsAny<AutoQCPeriods>(), username))
                .ReturnsAsync(autoQCPeriodResponse);

            // Act
            var result = await _controller.CreateAsyncAutoQc(username, autoQCPeriodCreateRequest, targetPeriodId) as ObjectResult;

            // Assert
            result.Should().NotBeNull();
            result.StatusCode.Should().Be(StatusCodes.Status207MultiStatus);
            result.Value.Should().BeOfType<AutoQCPeriodResponse>();
        }

        [Fact]
        public async Task CreateAsyncAutoQc_When_Failure_Expect_400Response()
        {
            // Arrange
            string username = "test_user";
            var targetPeriodId = 12345;
            var autoQCPeriodCreateRequest = new AutoQCPeriodCreateRequest
            {
                QCProjectIds = new List<int> { 1 }
            };

            _qcPeriodServiceMock
                .Setup(s => s.AutoQCPeriodCreateAsync(targetPeriodId, It.IsAny<AutoQCPeriods>(), username))
                .ReturnsAsync((AutoQCResponses)null);

            // Act
            var result = await _controller.CreateAsyncAutoQc(username, autoQCPeriodCreateRequest, targetPeriodId) as ObjectResult;

            result.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
        }



        [Fact]
        public async Task CreateAsyncBulkQc_When_Successful_Returns_207MultiStatus()
        {
            // Arrange
            string userName = "test_user";
            int qcProjectId = 123;
            int baseprojectId = 1234;
            var bulkQCPeriodRequest = new BulkQCPeriodRequest
            {
                StartPeriod = 1,
                EndPeriod = 10
            };
            var bulkQCPeriodDomainModel = new BulkQCPeriods
            {
                StartPeriod = bulkQCPeriodRequest.StartPeriod,
                EndPeriod = bulkQCPeriodRequest.EndPeriod
            };
            var expectedResult = new BulkQCResponses
            {
                BulkQCPeriods = new List<BulkQCPeriodRecord>
            {
                new BulkQCPeriodRecord { QCProjectId = qcProjectId, QCPeriodId = 1, StatusCode = (int)HttpStatusCode.Created, StatusMsg = "QC period created successfully" },
                new BulkQCPeriodRecord { QCProjectId = qcProjectId, QCPeriodId = 2, StatusCode = (int)HttpStatusCode.Created, StatusMsg = "QC period created successfully" }
            }
            };
            string countryId = "15";

            var bulkQCPeriodResponse = _fixture.Create<BulkQCResponses>(); 


            _qcPeriodServiceMock.Setup(x => x.CreateBulkQCPeriodAsync(qcProjectId, It.IsAny<BulkQCPeriods>(), userName))
                .ReturnsAsync(bulkQCPeriodResponse);
            _qcProjectServiceMock
                   .Setup(s => s.GetFilteredQCProjectAsync(It.IsAny<QCProjectCountryIds>()))
                   .ReturnsAsync(qcProjectId);

            // Act
            var result = await _controller.CreateAsyncBulkQc(userName, bulkQCPeriodRequest, qcProjectId,countryId) as ObjectResult;

            // Assert
            result.Should().NotBeNull();
            result.StatusCode.Should().Be(StatusCodes.Status207MultiStatus);
            result.Value.Should().BeOfType<BulkQCPeriodResponse>();
            var responseValue = result.Value as BulkQCPeriodResponse;
            responseValue.BulkQCPeriods.Should().NotBeNull();
           
        }

        [Fact]
        public async Task CreateAsyncBulkQc_When_Failure_Expect_400Response()
        {
            // Arrange
            string username = "test_user";
            var qcProjectId = 12345;
           
            var bulkQCPeriodRequest = _fixture.Create<BulkQCPeriodRequest>();
            string countryId = "15";
            _qcPeriodServiceMock
                .Setup(s => s.CreateBulkQCPeriodAsync(qcProjectId, It.IsAny<BulkQCPeriods>(), username))
                .ReturnsAsync((BulkQCResponses)null);
            _qcProjectServiceMock
                 .Setup(s => s.GetFilteredQCProjectAsync(It.IsAny<QCProjectCountryIds>()))
                 .ReturnsAsync(qcProjectId);
            // Act
            var result = await _controller.CreateAsyncBulkQc(username, bulkQCPeriodRequest, qcProjectId,countryId) as ObjectResult;

            // Assert
            result.Should().NotBeNull();
            result.StatusCode.Should().Be(StatusCodes.Status400BadRequest);

        }

        [Fact]
        public async Task AddAsync_When_ServiceReturnsNull_Expect_BadRequest()
        {
            // Arrange
            var countryId = "15";
            var userName = "testUser";
            var qcPeriodCreateRequest = new QCPeriodCreateRequest();
            QCPeriod qcPeriod = null;

            _qcPeriodServiceMock
                .Setup(s => s.AddAsyncQCPeriod(It.IsAny<QCPeriod>()))
                .ReturnsAsync(qcPeriod);

            _qcProjectServiceMock
                .Setup(s => s.GetAsync(It.IsAny<QCProjectCountries>()))
                .ReturnsAsync(new List<int> { qcPeriodCreateRequest.QCProjectId });

            // Act
            var result = await _controller.AddAsync(countryId,userName, qcPeriodCreateRequest);

            // Assert
            result.Should().BeOfType<BadRequestObjectResult>();
        }


        [Fact]
        public async Task UpdateAsyncQCPeriod_When_ServiceReturnsNull_Expect_404NotFound()
        {
            // Arrange
            var userName = "testUser";
            var qcperiodId = 1L;
            var editQCPeriodRequest = new QCPeriodEditRequest();
            QCPeriod qcPeriods = null;
            var countryIds = "15";

            _qcPeriodServiceMock
                .Setup(s => s.EditPeriodAsync(It.IsAny<long>(), It.IsAny<QCPeriodEdits>()))
                .ReturnsAsync(qcPeriods);

            _qcPeriodServiceMock
                .Setup(s => s.GetAuthorizedCountriesForPeriods(It.IsAny<QCPeriodCountries>()))
                .ReturnsAsync(new List<long> { qcperiodId });

            // Act
            var result = await _controller.UpdateAsyncQCPeriod(countryIds, userName, qcperiodId, editQCPeriodRequest);

            // Assert
            result.Should().BeOfType<NotFoundResult>();
        }

        [Fact]
        public async Task UpdateAsyncQCPeriod_When_UnauthorizedQCPeriod()
        {
            // Arrange
            var userName = "testUser";
            var qcperiodId = 1L;
            var editQCPeriodRequest = new QCPeriodEditRequest();
            QCPeriod qcPeriods = null;
            var countryIds = "15";

            _qcPeriodServiceMock
                .Setup(s => s.EditPeriodAsync(It.IsAny<long>(), It.IsAny<QCPeriodEdits>()))
                .ReturnsAsync(qcPeriods);

            _qcPeriodServiceMock
                .Setup(s => s.GetAuthorizedCountriesForPeriods(It.IsAny<QCPeriodCountries>()))
                .ReturnsAsync(new List<long> {});

            // Act
            var result = await _controller.UpdateAsyncQCPeriod(countryIds, userName, qcperiodId, editQCPeriodRequest);

            // Assert
            result.Should().BeOfType<StatusCodeResult>()
               .Which.StatusCode.Should().Be(StatusCodes.Status403Forbidden);
        }


        [Fact]
        public async Task CreateAsyncBulkQc_When_CountryIds_Are_Not_Authorized_Should_Return_403Forbidden()
        {
            // Arrange
            var countryIds = "1"; 
            var qcProjectId = 123; 
            var bulkQCPeriodRequest = _fixture.Create<BulkQCPeriodRequest>();
            int qcprojectnullresponse = 0;

            _qcProjectServiceMock
                    .Setup(s => s.GetFilteredQCProjectAsync(It.IsAny<QCProjectCountryIds>()))
                    .ReturnsAsync(qcprojectnullresponse);

            // Act
            var result = await _controller.CreateAsyncBulkQc("testUser", bulkQCPeriodRequest, qcProjectId, countryIds);

            // Assert
            result.Should().BeOfType<StatusCodeResult>()
                .Which.StatusCode.Should().Be(StatusCodes.Status403Forbidden);
        }

        [Fact]
        public async Task DeleteAsyncQCPeriod_When_InvalidOperationException_Thrown_Expect_400BadRequestResponse()
        {
            // Arrange
            string countryIds = null;
            var deleteQCPeriodRequest = new QCPeriodDeleteRequest
            {
                Ids = new List<long> { 101, 102, 103 }
            };

            _qcPeriodServiceMock.Setup(s => s.DeletePeriodAsync(It.IsAny<QCPeriodDeletes>()))
                                .ThrowsAsync(new InvalidOperationException("QC Period(s) cannot be deleted as they are currently in QC Status"));

            // Act
            var result = await _controller.DeleteAsyncQCPeriod(countryIds, deleteQCPeriodRequest) as ObjectResult;

            // Assert
            result.Should().NotBeNull();
            result.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
            result.Value.Should().BeEquivalentTo(new { message = "QC Period(s) cannot be deleted as they are currently in QC Status" });
        }



    }

}

