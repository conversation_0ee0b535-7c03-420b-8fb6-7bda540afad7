﻿using AutoFixture;
using AutoMapper;
using DWH.ProjectServices.API.Services.Interfaces;
using FluentAssertions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Moq;
using Microsoft.Extensions.Options;
using System.Net;
using BootstrapAPI.Core.Exception.Instances;
using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Presentation.Controllers;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ.Configuration;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ.Interfaces;

namespace DWH.ProjectServices.API.UnitTests.Presentation.Controllers
{
    public class QCProjectControllerTests
    {
        private readonly IFixture _fixture;
        private readonly IMapper _mapper;
        private readonly Mock<IQCProjectService> _qcProjectServiceMock;
        private readonly QCProjectsController _controller;

        public QCProjectControllerTests()
        {
            _fixture = new Fixture();
            var mockResponse = new Mock<HttpResponse>();
            var mockHttpContext = new Mock<HttpContext>();
            _qcProjectServiceMock = new Mock<IQCProjectService>();
            var rabbitMQSenderMock = new Mock<IRabbitMQSender>();
            var optionsMock = new Mock<IOptions<RabbitMQSettings>>();


            var mappingConfig = new MapperConfiguration(mc =>
            {
                mc.AddProfile(new API.Presentation.Profile.BaseProjectProfile());
                mc.AddProfile(new API.Presentation.Profile.QCProjectandPeriodProfile());
            });
            _mapper = mappingConfig.CreateMapper();

            _controller = new QCProjectsController(_mapper, _qcProjectServiceMock.Object);

            mockHttpContext.Setup(h => h.Response).Returns(mockResponse.Object);
        }

        [Fact]
        public async Task UpdateAsync_When_SuccessfullyAdded_Expect_200Response()
        {
            // Arrange
            var userid = _fixture.Create<int>();
            var username = _fixture.Create<string>();
            var countryIds = "15";
            var qcProjectId = 1;
            var qcProjectEditRequest = _fixture.Create<QCProjectEditRequest>();
            _fixture.Customize<QCProject>(b => b.With(x => x.Id, qcProjectId));
            var qcProjectEditResponse = _fixture.Create<QCProject>();

            _qcProjectServiceMock
                .Setup(s => s.UpdateAsync(qcProjectId, It.IsAny<QCProjectUpdates>())).ReturnsAsync(qcProjectEditResponse);

            _qcProjectServiceMock
              .Setup(s => s.GetAsync(It.IsAny<QCProjectCountries>())).ReturnsAsync(new List<int> { 1 });

            // Act
            var result = await _controller.UpdateAsync(countryIds,username, qcProjectEditResponse.Id, qcProjectEditRequest);

            // Assert

            result.Should().BeOfType<OkObjectResult>()
                            .Which.StatusCode.Should().Be(StatusCodes.Status200OK);
            result.Should().NotBeNull();
            _qcProjectServiceMock.Verify();
            var okResult = result as OkObjectResult;
            okResult.Value.Should().BeOfType<QCProjectEditResponse>();
        }

        [Fact]
        public async Task UpdateAsync_When_ServiceReturnsNull_Expect_404NotFound()
        {
            // Arrange
            var username = "testUser";
            var countryIds = "15";
            var qcProjectId = 1;
            var qcProjectEditRequest = new QCProjectEditRequest();
            QCProject qcProjectEditResponse = null; 

            _qcProjectServiceMock
                .Setup(s => s.UpdateAsync(qcProjectId, It.IsAny<QCProjectUpdates>()))
                .ReturnsAsync(qcProjectEditResponse);
            _qcProjectServiceMock
            .Setup(s => s.GetAsync(It.IsAny<QCProjectCountries>())).ReturnsAsync(new List<int> { 1 });

            // Act
            var result = await _controller.UpdateAsync(countryIds,username, qcProjectId, qcProjectEditRequest);

            // Assert
            result.Should().BeOfType<NotFoundResult>();
        }

        [Fact]
        public async Task GetQCProjectAsync_When_ServiceReturnsValid_Expect_200Response()
        {
            // Arrange
            List<int> expectedResult = new List<int> { 1,2,3};
            var qcProjectCountryRequest = _fixture.Create<QCProjectCountryRequest>();

            _qcProjectServiceMock
                .Setup(s => s.GetAsync(It.IsAny<QCProjectCountries>()))
                .ReturnsAsync(expectedResult);

            // Act
            var result = await _controller.GetQCProjectAsync(qcProjectCountryRequest);

            // Assert
            result.Should().BeOfType<OkObjectResult>();
            var okResult = result as OkObjectResult;
            okResult.Value.Should().BeOfType<List<int>>();
        }

        [Fact]
        public async Task GetQCProjectAsync_When_ServiceReturnsEmptyList_Expect_403Forbidden()
        {
            // Arrange
            List<int> expectedResult = new List<int>();
            var qcProjectCountryRequest = _fixture.Create<QCProjectCountryRequest>();
            var qcProjectCountryModel = _mapper.Map<QCProjectCountries>(qcProjectCountryRequest);

            _qcProjectServiceMock
                .Setup(s => s.GetAsync(It.IsAny<QCProjectCountries>()))
                .ReturnsAsync(expectedResult); // Returning an empty list

            // Act
            var result = await _controller.GetQCProjectAsync(qcProjectCountryRequest);

            // Assert
            result.Should().BeOfType<StatusCodeResult>()
                .Which.StatusCode.Should().Be(StatusCodes.Status403Forbidden);
        }




    }
}


