﻿using AutoFixture;
using AutoMapper;
using DWH.ProjectServices.API.Domain.Enum;
using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Infrastructure.Persistence;
using DWH.ProjectServices.API.Infrastructure.Persistence.Entities;
using DWH.ProjectServices.API.Infrastructure.Persistence.Profile;
using DWH.ProjectServices.API.Infrastructure.Persistence.Repositories.Interfaces;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ.Configuration;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ.Interfaces;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;
using DWH.ProjectServices.API.Presentation.Controllers;
using DWH.ProjectServices.API.Services.Interfaces;
using FluentAssertions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Moq;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using Xunit.Extensions.AssertExtensions;

namespace DWH.ProjectServices.API.UnitTests.Presentation.Controllers
{
    public class RetailerSeperationControllerTests
    {
        private readonly IFixture _fixture;
        private readonly IMapper _mapper;
        private readonly Mock<IRetailerSeperationService> _retailerSeperationServiceMock;
        private readonly RetailerSeperationController _controller;
        private readonly Mock<IBaseProjectService> _baseProjectServiceMock;

        public RetailerSeperationControllerTests()
        {
            _fixture = new Fixture();
            var mockResponse = new Mock<HttpResponse>();
            var mockHttpContext = new Mock<HttpContext>();
            _retailerSeperationServiceMock = new Mock<IRetailerSeperationService>();
            _baseProjectServiceMock = new Mock<IBaseProjectService>();
            var mappingConfig = new MapperConfiguration(mc =>
            {
                mc.AddProfile(new RetailerSeperationProfile());
            });
            _mapper = mappingConfig.CreateMapper();
            
            _controller = new RetailerSeperationController(_mapper, _retailerSeperationServiceMock.Object, _baseProjectServiceMock.Object);

            mockHttpContext.Setup(h => h.Response).Returns(mockResponse.Object);
        }

        [Fact]
        public async Task AddAsync_When_SuccessfullyAdded_Expect_201Response()
        {
            // Arrange
            var username = _fixture.Create<string>();
            var email = _fixture.Create<string>();
            var newRetailerSeperationRequest = _fixture.Create<RetailerSeperationCreateRequest>();
            var retailerSeperationResponse = _fixture.Create<RetailerSeperationRequest>();

            // Mock HttpContext
            var mockHttpContext = new Mock<HttpContext>();
            var mockRequest = new Mock<HttpRequest>();
            var mockResponse = new Mock<HttpResponse>();

            // Setup HttpContext.Request
            mockHttpContext.SetupGet(c => c.Request).Returns(mockRequest.Object);
            mockHttpContext.SetupGet(c => c.Response).Returns(mockResponse.Object);
            mockRequest.SetupGet(r => r.Scheme).Returns("https");
            mockRequest.SetupGet(r => r.Host).Returns(new HostString("example.com"));

            // Assign mock HttpContext to controller
            _controller.ControllerContext = new ControllerContext { HttpContext = mockHttpContext.Object };

            // Setup service mock
            _retailerSeperationServiceMock
                .Setup(s => s.AddAsync(It.IsAny<RetailerSeperationRequest>(), email))
                .ReturnsAsync(retailerSeperationResponse);

            // Act
            var result = await _controller.AddAsync(username, email, newRetailerSeperationRequest) as ObjectResult;

            // Assert
            result.Should().NotBeNull();
            result.StatusCode.Should().Be(StatusCodes.Status201Created);
        }

        [Fact]
        public async Task AddAsync_When_Unsuccessful_Returns_BadRequest()
        {
            // Arrange
            var username = _fixture.Create<string>();
            var email = _fixture.Create<string>();
            var newRetailerSeperationRequest = _fixture.Create<RetailerSeperationCreateRequest>();

            // Mock HttpContext
            var mockHttpContext = new Mock<HttpContext>();
            var mockRequest = new Mock<HttpRequest>();
            var mockResponse = new Mock<HttpResponse>();

            // Setup HttpContext.Request
            mockHttpContext.SetupGet(c => c.Request).Returns(mockRequest.Object);
            mockHttpContext.SetupGet(c => c.Response).Returns(mockResponse.Object);
            mockRequest.SetupGet(r => r.Scheme).Returns("https");
            mockRequest.SetupGet(r => r.Host).Returns(new HostString("example.com"));

            // Assign mock HttpContext to controller
            _controller.ControllerContext = new ControllerContext { HttpContext = mockHttpContext.Object };

            // Setup service mock to return null to simulate failure
            _retailerSeperationServiceMock
                .Setup(s => s.AddAsync(It.IsAny<RetailerSeperationRequest>(), email))
                .ReturnsAsync((RetailerSeperationRequest)null);

            // Act
            var result = await _controller.AddAsync(username, email, newRetailerSeperationRequest) as ObjectResult;

            // Assert
            result.Should().NotBeNull();
            result.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
        }

        [Fact]
        public async Task GetRetailerSeperationsAsync_Expect_OKResult()
        {
            // Arrange
            var retailerSeperationLists = _fixture.Create<RetailerSeperationLists>();
            var authorizedRetailerSeperation = _fixture.Create<RetailerSeperationListResponse>();
            _retailerSeperationServiceMock.Setup(s => s.GetAsyncList(It.IsAny<RetailerSeperationsLists>()))
            .ReturnsAsync(retailerSeperationLists);

            var retailerSeperationListRequest = _fixture.Create<RetailerSeperationListRequest>();

            // Act
            var result = await _controller.GetAsyncList(retailerSeperationListRequest);

            // Assert
            var objectResult = result as ObjectResult;
            objectResult.Should().NotBeNull();
            objectResult.StatusCode.Should().Be(200);
        }

        [Fact]
        public async Task GetRetailerSeperationsAsync_When_EmptyResult_Expect_NotFoundResult()
        {
            // Arrange
            RetailerSeperationLists emptyRetailerSeperationLists = null;
            _retailerSeperationServiceMock.Setup(s => s.GetAsyncList(It.IsAny<RetailerSeperationsLists>()))
                .ReturnsAsync(emptyRetailerSeperationLists);

            var retailerSeperationListRequest = _fixture.Create<RetailerSeperationListRequest>();

            // Act
            var result = await _controller.GetAsyncList(retailerSeperationListRequest);

            // Assert
            var notFoundResult = result as NotFoundResult;
            notFoundResult.Should().NotBeNull();
            notFoundResult.StatusCode.Should().Be(404);
        }

        [Fact]
        public async Task GetUserListAsync_Expect_OKResult()
        {
            // Arrange
            var userList = _fixture.Create<IReadOnlyList<string>>();
            _retailerSeperationServiceMock.Setup(s => s.GetUsersList())
                .ReturnsAsync(userList);

            // Act
            var result = await _controller.GetUserList();

            // Assert
            var objectResult = result as ObjectResult;
            objectResult.Should().NotBeNull();
            objectResult.StatusCode.Should().Be(200);
            objectResult.Value.Should().BeEquivalentTo(userList);
        }

        [Fact]
        public async Task GetUserListAsync_WhenListIsEmpty_Expect_OKResultWithEmptyList()
        {
            // Arrange
            var emptyUserList = new List<string>().AsReadOnly();
            _retailerSeperationServiceMock.Setup(s => s.GetUsersList())
                .ReturnsAsync(emptyUserList);

            // Act
            var result = await _controller.GetUserList();

            // Assert
            var objectResult = result as ObjectResult;
            objectResult.Should().NotBeNull();
            objectResult.StatusCode.Should().Be(200);
            objectResult.Value.Should().BeEquivalentTo(emptyUserList);
        }

        [Fact]
        public async Task CreateAsyncIRSeparation_When_SuccessfullyProcessed_Expect_207Response()
        {
            // Arrange
            var username = "testUser";
            var irSeparationRequest = new IRSeparationRequest
            {
                retailerSeperationRequestIds = new List<int> { 1, 2, 3 }
            };

            var baseProjectIds = new List<int> { 10, 20, 30 };
            var baseProjects = new List<BaseProject>
                                {
                                    new BaseProject { Id = 10, Name = "BaseProject 1" },
                                    new BaseProject { Id = 20, Name = "BaseProject 2" },
                                    new BaseProject { Id = 30, Name = "BaseProject 3" }
                                };

            var irResult = new IRSeparationResult { IndustryProject = 1, RetailerProject = 21 };


            _retailerSeperationServiceMock
                .Setup(s => s.GetSourceBaseProjects(It.IsAny<int>()))
                .ReturnsAsync(baseProjectIds);

            _retailerSeperationServiceMock
                .Setup(s => s.AddDetailAndUpdateStatusAsync(It.IsAny<RetailerSeperationRequestDetail>()))
                .Returns(Task.CompletedTask);

            var mockHttpContext = new Mock<HttpContext>();
            var mockResponse = new Mock<HttpResponse>();
            _controller.ControllerContext = new ControllerContext { HttpContext = mockHttpContext.Object };
            mockResponse.SetupGet(r => r.Headers).Returns(new HeaderDictionary());
            mockHttpContext.SetupGet(c => c.Response).Returns(mockResponse.Object);

            // Act
            var result = await _controller.CreateAsyncIRSeparation(username, irSeparationRequest) as ObjectResult;

            // Assert
            result.Should().NotBeNull();
            result.StatusCode.Should().Be(StatusCodes.Status207MultiStatus);

            var responseContent = result.Value as IRSeparationRecords;
            responseContent.Should().NotBeNull();
            responseContent.IRSeparationRecord.Should().HaveCount(irSeparationRequest.retailerSeperationRequestIds.Count);
        }

        [Fact]
        public void IRSeparationRequest_When_IdsAreEmpty_ShouldReturnValidationResult()
        {
            // Arrange
            var IRSeparationRequest = new IRSeparationRequest
            {
                 retailerSeperationRequestIds= new List<int>()
            };

            // Act
            var validationResults = IRSeparationRequest.Validate(new ValidationContext(IRSeparationRequest)).ToList();

            // Assert
            validationResults.ShouldNotBeEmpty();
            validationResults.First().ErrorMessage.ShouldEqual("Must have atleast one retailerSeperationRequestId to proceed");
        }

        [Fact]
        public async Task GetAsync_When_SuccessfullyAdded_Expect_200Response()
        {
            // Arrange
            var requestId = 1;
            var rsRequests = _fixture.Create<RetailerSeperationRequest>();
            _retailerSeperationServiceMock.Setup(s => s.GetAsync(requestId))
                .ReturnsAsync(rsRequests);

            // Act
            var result = await _controller.GetAsync(requestId);

            // Assert
            var objectResult = result as ObjectResult;
            objectResult.Should().NotBeNull();
            objectResult.StatusCode.Should().Be(200);
        }

        [Fact]
        public async Task GetAsync_When_ServiceReturnsNull_Expect_404NotFound()
        {
            // Arrange
            var requestId = 1;
            RetailerSeperationRequest rsRequests = null;
            _retailerSeperationServiceMock.Setup(s => s.GetAsync(requestId))
                .ReturnsAsync(rsRequests);

            // Act
            var result = await _controller.GetAsync(requestId);

            // Assert
            result.Should().BeOfType<NotFoundResult>();
        }

        [Fact]
        public async Task UpdateAsync_When_SuccessfullyUpdated_Expect_200Response()
        {
            // Arrange
            var username = _fixture.Create<string>();
            var userEmail = _fixture.Create<string>();
            var rsEditRequest = _fixture.Create<RetailerSeperationEditRequest>();
            var rsRequests = _fixture.Create<RetailerSeperationRequest>();
            var requestId = 1;
            var countryIds = "15";


            _retailerSeperationServiceMock
                .Setup(s => s.UpdateAsync(username,userEmail, It.IsAny<RetailerSeperationRequest>(), It.IsAny<string>())).ReturnsAsync(rsRequests);

            _retailerSeperationServiceMock
                .Setup(s => s.GetAuthorizedBaseProjectsByCountry(It.IsAny<RetailerSeparationCountries>()))
                .ReturnsAsync(new List<int> { requestId });
            // Act
            var result = await _controller.UpdateAsync(countryIds, username,userEmail, requestId, rsEditRequest) as ObjectResult;

            // Assert
            result.Should().NotBeNull();
            result.StatusCode.Should().Be(StatusCodes.Status200OK);
        }

        [Fact]
        public async Task UpdateAsync_When_ServiceReturnsNull_Expect_404NotFound()
        {
            // Arrange
            var userName = "test User";
            var userEmail = _fixture.Create<string>();
            var requestId = 1;
            var rsEditRequest = _fixture.Create<RetailerSeperationEditRequest>();
            RetailerSeperationRequest rsRequests = null;
            var countryIds = "15";

            _retailerSeperationServiceMock
                .Setup(s => s.UpdateAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<RetailerSeperationRequest>(), It.IsAny<string>()))
                .ReturnsAsync(rsRequests);

            _retailerSeperationServiceMock
                .Setup(s => s.GetAuthorizedBaseProjectsByCountry(It.IsAny<RetailerSeparationCountries>()))
                .ReturnsAsync(new List<int> { requestId });

            // Act
            var result = await _controller.UpdateAsync(countryIds, userName,userEmail, requestId, rsEditRequest);

            // Assert
            result.Should().BeOfType<NotFoundResult>();
        }

        [Fact]
        public async Task UpdateAsync_When_UnauthorizedCountryExists()
        {
            // Arrange
            var userName = "testUser";
            var userEmail = "testEmail";
            var requestId = 1;
            var rsEditRequest = _fixture.Create<RetailerSeperationEditRequest>();
            var countryIds = "15";

            RetailerSeperationRequest rsRequests = null;

            _retailerSeperationServiceMock
                .Setup(s => s.UpdateAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<RetailerSeperationRequest>(), It.IsAny<string>()))
                .ReturnsAsync(rsRequests);

            _retailerSeperationServiceMock
                .Setup(s => s.GetAuthorizedBaseProjectsByCountry(It.IsAny<RetailerSeparationCountries>()))
                .ReturnsAsync(new List<int> { });

            // Act
            var result = await _controller.UpdateAsync(countryIds, userName, userEmail, requestId, rsEditRequest);

            // Assert
            result.Should().BeOfType<StatusCodeResult>()
               .Which.StatusCode.Should().Be(StatusCodes.Status403Forbidden);
        }

        [Fact]
        public async Task UpdateStatusAsync_ReturnsMultiStatus_WhenValidRequest()
        {
            // Arrange
            var request = new RetailerSeperationStatusRequest
            {
                retailerSeperationRequestIds = new List<int> { 1, 2 },
                statusId = 1,
                reason = "Test Reason"
            };

            _retailerSeperationServiceMock
                .Setup(s => s.UpdateStatusAsync(It.IsAny<RetailerSeparationStatusDetails>()))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.UpdateStatusAsync("testUser","testEmail", request);

            // Assert
            result.Should().NotBeNull();
            var multiStatusResult = result.Should().BeOfType<ObjectResult>().Subject;
            multiStatusResult.StatusCode.Should().Be(StatusCodes.Status207MultiStatus);
            var response = multiStatusResult.Value.Should().BeOfType<RetailerSeperationStatusResponse>().Subject;
            response.status.Count.Should().Be(2);
            response.status.Should().AllSatisfy(status => status.Should().BeTrue());
        }

        [Fact]
        public async Task UpdateStatusAsync_ReturnsFalseRequest_WhenIncorrectRequestIdsProvided()
        {
            // Arrange
            var request = new RetailerSeperationStatusRequest { retailerSeperationRequestIds = new List<int> { 99999 } };

            _retailerSeperationServiceMock
                .Setup(s => s.UpdateStatusAsync(It.IsAny<RetailerSeparationStatusDetails>()))
                .ReturnsAsync(false);

            // Act
            var result = await _controller.UpdateStatusAsync("testUser", "testEmail", request);

            // Assert
            result.Should().NotBeNull();
            var multiStatusResult = result.Should().BeOfType<ObjectResult>().Subject;
            multiStatusResult.StatusCode.Should().Be(StatusCodes.Status207MultiStatus);
            var response = multiStatusResult.Value.Should().BeOfType<RetailerSeperationStatusResponse>().Subject;
            response.status.Should().Contain(false);
        }
        [Fact]
        public async Task DeleteAsyncSourceBP_When_Called_With_ValidData_Should_Return_207MultiStatus()
        {
            // Arrange
            var countryIds = "1,2,3";
            var deleteRequest = new IRSeparationSourceBaseprojectsDeleteRequest
            {
                RetailerSeperationIds = new List<int> { 100, 200 }
            };
            var authorizedBaseProjects = new List<int> { 100 };

            _retailerSeperationServiceMock.Setup(s => s.GetAuthorizedRetailerSeperationsByCountry(It.IsAny<RetailerSeparationCountries>()))
                .ReturnsAsync(authorizedBaseProjects);

            _retailerSeperationServiceMock.Setup(s => s.DeleteSourceBPAsync(It.IsAny<IRSeperationDeletes>()))
                    .ReturnsAsync(authorizedBaseProjects.Select(id =>
                    new ResponseInfoRetailerSeperation(id.ToString(), 200, "Deleted", id)).ToList());


            // Act
            var result = await _controller.DeleteAsyncSourceBP(null, countryIds, deleteRequest);

            // Assert
            result.Should().BeOfType<ObjectResult>()
                .Which.StatusCode.Should().Be(StatusCodes.Status207MultiStatus);
            var objectResult = result as ObjectResult;
            objectResult.Value.Should().BeOfType<List<ResponseInfoRetailerSeperation>>();
        }

        [Fact]
        public async Task DeleteAsyncSourceBP_When_CountryIds_Are_NotAuthorized_Should_Return_403Forbidden()
        {
            // Arrange
            var countryIds = "1,2,3";
            var deleteRequest = new IRSeparationSourceBaseprojectsDeleteRequest
            {
                RetailerSeperationIds = new List<int> { 100, 200 }
            };

            _retailerSeperationServiceMock.Setup(s => s.GetAuthorizedRetailerSeperationsByCountry(It.IsAny<RetailerSeparationCountries>()))
                .ReturnsAsync(new List<int>());

            // Act
            var result = await _controller.DeleteAsyncSourceBP(null, countryIds, deleteRequest);

            // Assert
            result.Should().BeOfType<StatusCodeResult>()
                .Which.StatusCode.Should().Be(StatusCodes.Status403Forbidden);
        }

        [Fact]
        public async Task DeleteAsyncSourceBP_When_Called_Without_CountryIds_Should_Return_207MultiStatus()
        {
            // Arrange
            string countryIds = null; // Simulate the absence of header
            var deleteRequest = new IRSeparationSourceBaseprojectsDeleteRequest
            {
                RetailerSeperationIds = new List<int> { 100, 200 }
            };

            _retailerSeperationServiceMock.Setup(s => s.DeleteSourceBPAsync(It.IsAny<IRSeperationDeletes>()))
                .ReturnsAsync(new List<ResponseInfoRetailerSeperation>
                {
            new ResponseInfoRetailerSeperation("12345", 200, "Deleted",123)
                });

            // Act
            var result = await _controller.DeleteAsyncSourceBP(null, countryIds, deleteRequest);

            // Assert
            result.Should().BeOfType<ObjectResult>()
                .Which.StatusCode.Should().Be(StatusCodes.Status207MultiStatus);
        }

        [Fact]
        public async Task DeleteAsyncSourceBP_InvalidModelState_ReturnsBadRequest()
        {
            // Arrange
            var invalidRequest = new IRSeparationSourceBaseprojectsDeleteRequest
            {
                RetailerSeperationIds = new List<int>()
            };

            // Act
            var result = await _controller.DeleteAsyncSourceBP(null,null, invalidRequest) as ObjectResult;

            // Assert
            result.StatusCode.Should().Be(StatusCodes.Status400BadRequest);
        }

        [Fact]
        public async Task AddAsync_When_QCStatusExists_Expect_BadRequest()
        {
            // Arrange
            var userName = _fixture.Create<string>();
            var email = _fixture.Create<string>();
            var createRequest = _fixture.Create<RetailerSeperationCreateRequest>();

            var retailerSeperation = _fixture.Create<RetailerSeperationRequest>();

            var exceptionMessage = "Selected BPs have at least one QC Period in QC Status. (1, 2)";
            _retailerSeperationServiceMock
                .Setup(service => service.AddAsync(It.IsAny<RetailerSeperationRequest>(), email))
                .ThrowsAsync(new InvalidOperationException(exceptionMessage));

            // Act
            var result = await _controller.AddAsync(userName, email, createRequest);

            // Assert
            result.Should().BeOfType<BadRequestObjectResult>();
            var badRequestResult = result as BadRequestObjectResult;
            badRequestResult!.Value.Should().BeEquivalentTo(new { message = exceptionMessage });

        }

        [Fact]
        public async Task CreateAsyncIRSeparationBaseProject_When_SuccessfullyProcessed_Expect_200Response()
        {
            // Arrange
            var username = "testUser";
            var irSeparationBaseProjectRequest = new IRSeparationBaseProjectRequest
            {
                SourceBaseProjectId = 10,
                retailerSeparationId = 1,
                indexSourceBP=1,
                totalSourceBP=1
            };
            var retailerBP = 20;
            _retailerSeperationServiceMock
                .Setup(s => s.PerformRetailerSeparationAsync(
                    irSeparationBaseProjectRequest.SourceBaseProjectId,
                    (int)ProjectSubType.Industry,
                    username,
                    irSeparationBaseProjectRequest.retailerSeparationId,
                    irSeparationBaseProjectRequest.indexSourceBP,
                    irSeparationBaseProjectRequest.totalSourceBP))
                .ReturnsAsync(retailerBP);

            var mockHttpContext = new Mock<HttpContext>();
            var mockResponse = new Mock<HttpResponse>();
            _controller.ControllerContext = new ControllerContext { HttpContext = mockHttpContext.Object };
            mockResponse.SetupGet(r => r.Headers).Returns(new HeaderDictionary());
            mockHttpContext.SetupGet(c => c.Response).Returns(mockResponse.Object);

            // Act
            var result = await _controller.CreateAsyncIRSeparationBaseProject(username, irSeparationBaseProjectRequest)  as ObjectResult; ;

            // Assert
            result.Should().NotBeNull();
            result.StatusCode.Should().Be(StatusCodes.Status200OK);

            var responseContent = result.Value as IRSeparationResponseBaseProject;
            responseContent.Should().NotBeNull();
            responseContent.RetailerProject.Should().Be(retailerBP);

        }



    }
}
