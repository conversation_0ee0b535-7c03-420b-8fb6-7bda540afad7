﻿using BootstrapAPI.Core.Exception.Instances;
using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Infrastructure.Persistence.Entities;
using DWH.ProjectServices.API.Infrastructure.Persistence.Repositories.Interfaces;
using DWH.ProjectServices.API.Services;
using FluentAssertions;
using Moq;
using System.Collections.Generic;
using System.Threading.Tasks;
using Xunit;

namespace DWH.ProjectServices.API.UnitTests.Services
{
    public class BaseProjectDataTypeServiceTests
    {
        private readonly Mock<IBaseProjectDataTypeRepository> _baseProjectDataTypeRepositoryStub;
        private readonly BaseProjectDataTypeService _service;

        public BaseProjectDataTypeServiceTests()
        {
            _baseProjectDataTypeRepositoryStub = new Mock<IBaseProjectDataTypeRepository>();
            _service = new BaseProjectDataTypeService(_baseProjectDataTypeRepositoryStub.Object);
        }

        [Fact]
        public async Task GetAllAsync_WhenCalled_WithData_ReturnsBaseDataTypes()
        {
            // Arrange
            var baseProjectDataTypeEntities = new List<DataTypes>
            {
                new DataTypes { Id = 1, Name = "DataType1", Description ="DataTypeDesc1" },
                new DataTypes { Id = 2, Name = "DataType2", Description ="DataTypeDesc2"  }
            };

            var expectedBaseProjectDataTypes = new List<DataTypes>
            {
                new DataTypes { Id = 1, Name = "DataType1", Description="DataTypeDesc1"  },
                new DataTypes { Id = 2, Name = "DataType2", Description="DataTypeDesc2"  }
            };

            _baseProjectDataTypeRepositoryStub
                .Setup(repo => repo.GetAllAsync())
                .ReturnsAsync(baseProjectDataTypeEntities);

            // Act
            var result = await _service.GetAllAsync();

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEquivalentTo(expectedBaseProjectDataTypes);
        }

        [Fact]
        public async Task GetAllAsync_WhenCalled_WithNoData_ReturnsEmptyCollection()
        {
            // Arrange
            var emptyBaseProjectDataTypeEntities = new List<DataTypes>();

            _baseProjectDataTypeRepositoryStub
                .Setup(repo => repo.GetAllAsync())
                .ReturnsAsync(emptyBaseProjectDataTypeEntities);

            // Act
            var result = await _service.GetAllAsync();

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        [Fact]
        public async Task GetAllAsync_WhenCalled_AndRepositoryThrowsException_ThrowsException()
        {
            // Arrange
            _baseProjectDataTypeRepositoryStub
                .Setup(repo => repo.GetAllAsync())
                .ThrowsAsync(new EntityNotExistsException($"BaseProject Panels Not Found "));

            // Act
            var act = async () => await _service.GetAllAsync();

            // Assert
            await act.Should().ThrowAsync<EntityNotExistsException>();
        }


    }
}
