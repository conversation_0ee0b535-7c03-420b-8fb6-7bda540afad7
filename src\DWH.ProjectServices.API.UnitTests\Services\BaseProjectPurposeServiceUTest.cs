﻿using BootstrapAPI.Core.Exception.Instances;
using DWH.ProjectServices.API.Infrastructure.Persistence.Entities;
using DWH.ProjectServices.API.Infrastructure.Persistence.Repositories.Interfaces;
using DWH.ProjectServices.API.Services;
using FluentAssertions;
using Moq;
using System.Collections.Generic;
using System.Threading.Tasks;
using DWH.ProjectServices.API.Domain.Models;
using Xunit;

namespace DWH.ProjectServices.API.UnitTests.Services
{
    public class BaseProjectPurposeServiceTests
    {
        private readonly Mock<IBaseProjectPurposeRepository> _baseProjectPurposeRepositoryStub;
        private readonly BaseProjectPurposeService _service;

        public BaseProjectPurposeServiceTests()
        {
            _baseProjectPurposeRepositoryStub = new Mock<IBaseProjectPurposeRepository>();
            _service = new BaseProjectPurposeService(_baseProjectPurposeRepositoryStub.Object);
        }

        [Fact]
        public async Task GetAllAsync_WhenCalled_WithData_ReturnsBaseProjectPurposes()
        {
            // Arrange
            var baseProjectPurposeEntities = new List<Purposes>
            {
                new Purposes { Id = 1, Name = "Purpose1" },
                new Purposes { Id = 2, Name = "Purpose2" }
            };

            var expectedBaseProjectPurposes = new List<Purposes>
            {
                new Purposes { Id = 1, Name = "Purpose1" },
                new Purposes { Id = 2, Name = "Purpose2" }
            };

            _baseProjectPurposeRepositoryStub
                .Setup(repo => repo.GetAllAsync())
                .ReturnsAsync(baseProjectPurposeEntities);

            // Act
            var result = await _service.GetAllAsync();

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEquivalentTo(expectedBaseProjectPurposes);
        }

        [Fact]
        public async Task GetAllAsync_WhenCalled_WithNoData_ReturnsEmptyCollection()
        {
            // Arrange
            var emptyBaseProjectPurposeEntities = new List<Purposes>();

            _baseProjectPurposeRepositoryStub
                .Setup(repo => repo.GetAllAsync())
                .ReturnsAsync(emptyBaseProjectPurposeEntities);

            // Act
            var result = await _service.GetAllAsync();

            // Assert
            result.Should().NotBeNull();
            result.Should().BeEmpty();
        }

        [Fact]
        public async Task GetAllAsync_WhenCalled_AndRepositoryThrowsException_ThrowsException()
        {
            // Arrange
            _baseProjectPurposeRepositoryStub
                .Setup(repo => repo.GetAllAsync())
                .ThrowsAsync(new EntityNotExistsException($"BaseProject Panels Not Found "));

            // Act
            var act = async () => await _service.GetAllAsync();

            // Assert
            await act.Should().ThrowAsync<EntityNotExistsException>();
        }


    }
}
