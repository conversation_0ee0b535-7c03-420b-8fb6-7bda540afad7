﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ.Constants;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ;
using DWH.ProjectServices.API.Services.Interfaces;
using DWH.ProjectServices.API.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using RabbitMQ.Client.Events;
using RabbitMQ.Client;

namespace DWH.ProjectServices.API.UnitTests.Services
{
    public class ErrorHandlerTests
    {
        private readonly Mock<IChannel> _mockChannel;
        private readonly Mock<ILogger<MessageConsumer>> _mockLogger;
        private readonly Mock<IConfiguration> _mockConfiguration;
        private readonly IErrorHandler _errorHandler;
        private readonly Mock<IBasicProperties> _mockBasicProperties;
        private readonly BasicDeliverEventArgs _eventArgs;
        public ErrorHandlerTests()
        {
            _mockChannel = new Mock<IChannel>();
            _mockLogger = new Mock<ILogger<MessageConsumer>>();
            _mockConfiguration = new Mock<IConfiguration>();
            _errorHandler = new ErrorHandler(_mockConfiguration.Object, _mockLogger.Object);
            _mockBasicProperties = new Mock<IBasicProperties>();
            var headers = new Dictionary<string, object>
            {
                { "retry-count", 0 }
            };
            _mockBasicProperties.Setup(s => s.Headers).Returns(headers);

            // Mock BasicDeliverEventArgs
            _eventArgs = new BasicDeliverEventArgs(
                "test-consumer",
                1,
                false,
                RetailerSeparationConstants.RetailerSeparationExchange,
                RMQConstants.RetailerSeparationProjectServicesRoutingKey,
                _mockBasicProperties.Object,
                new ReadOnlyMemory<byte>(new byte[] { 123, 34, 73, 100, 34, 58, 34, 99, 48, 97, 57, 57, 52, 101, 51, 45, 99, 97, 50, 57, 45, 52, 97, 55, 97, 45, 97, 50, 49, 52, 45, 49, 48, 101, 102, 50, 56, 56, 55, 57, 97, 49, 101, 34, 44, 34, 83, 121, 110, 99, 105, 110, 103, 69, 110, 116, 105, 116, 121, 73, 100, 34, 58, 49, 48, 48, 48, 48, 52, 48, 57, 125 }),
                new CancellationToken()
            );

            // Mock Configuration Value
            var mockSection = new Mock<IConfigurationSection>();
            mockSection.Setup(s => s.Value).Returns("3");
            _mockConfiguration.Setup(c => c.GetSection("RetryLimit:Value")).Returns(mockSection.Object);
        }

        [Fact]
        public void HandleErrorMessages_AcknowledgesMessage()
        {
            // Arrange
            _mockChannel
                .Setup(c => c.BasicAckAsync(It.IsAny<ulong>(), It.IsAny<bool>(), It.IsAny<CancellationToken>()))
                .Returns(ValueTask.CompletedTask);

            // Act
            _errorHandler.HandleErrorMessages(_mockChannel.Object, _eventArgs);

            // Assert
            _mockChannel.Verify(c => c.BasicAckAsync(_eventArgs.DeliveryTag, false, It.IsAny<CancellationToken>()), Times.Once);
        }
    }
}
