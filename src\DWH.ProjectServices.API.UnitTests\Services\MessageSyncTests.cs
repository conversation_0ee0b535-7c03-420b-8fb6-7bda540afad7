﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using AutoFixture;
using AutoMapper;
using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Infrastructure.Persistence.Repositories.Interfaces;
using DWH.ProjectServices.API.Infrastructure.Persistence.Repositories;
using DWH.ProjectServices.API.Infrastructure.Persistence;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ.Constants;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;
using DWH.ProjectServices.API.Services.Helper.Interface;
using DWH.ProjectServices.API.Services.Interfaces;
using DWH.ProjectServices.API.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using RabbitMQ.Client.Events;
using RabbitMQ.Client;
using TestSupport.Helpers;
using DWH.ProjectServices.API.Services.Helper;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;

namespace DWH.ProjectServices.API.UnitTests.Services
{
    public class MessageSyncTests
    {
        Mock<ILogger<MessageSync>> _mockLogger;
        IMessageSync _msgSyncMock;
        IConfigurationRoot _configuration;
        Mock<IProjectServicesHelper> _projectServicesHelper;

        public MessageSyncTests()
        {
            var configurationSettings = new Dictionary<string, string>
                                        {
                                            { "ExchangeEntitySettings:RetailerSeparationExchangeEnabled", "true" },
                                            { "WebServiceClient:BaseAddress:ProjectServicesAPI", "https://projectservices-api.t1.dwh.in.gfk.com/" }, 
                                        };

            var configurationBuilder = new ConfigurationBuilder();
            configurationBuilder.AddInMemoryCollection(configurationSettings);
            _configuration = configurationBuilder.Build();
            _mockLogger = new Mock<ILogger<MessageSync>>();
            _projectServicesHelper= new Mock<IProjectServicesHelper>();

            _msgSyncMock = new MessageSync(_mockLogger.Object, _configuration,_projectServicesHelper.Object);
        }

        [Fact]
        public async Task PerformSync_BaseProject_Exchange_ShouldExecute_DoCreate_Returns_Success()
        {
            // Arrange
            var eventArgs = new BasicDeliverEventArgs(
                "test-consumer",
                1,
                false,
                RetailerSeparationConstants.RetailerSeparationExchange,
                RMQConstants.RetailerSeparationProjectServicesRoutingKey,
                new Mock<IBasicProperties>().Object,
                new ReadOnlyMemory<byte>(new byte[] { 123, 34, 73, 100, 34, 58, 34, 99, 48, 97, 57, 57, 52, 101, 51, 45, 99, 97, 50, 57, 45, 52, 97, 55, 97, 45, 97, 50, 49, 52, 45, 49, 48, 101, 102, 50, 56, 56, 55, 57, 97, 49, 101, 34, 44, 34, 83, 121, 110, 99, 105, 110, 103, 69, 110, 116, 105, 116, 121, 73, 100, 34, 58, 49, 48, 48, 48, 48, 52, 48, 57, 125 }),
                new CancellationToken()
            );

            Fixture _fixture = new Fixture();
            var mockBaseProjectDM = _fixture.Create<BaseProject>();

            _projectServicesHelper.Setup(client => client.PerformRetailerSeparation(It.IsAny<IRSeparationBaseProjectRequest>(),It.IsAny<string>()))
            .Returns(Task.CompletedTask);

            // Act
            await _msgSyncMock.PerformSync(eventArgs);

            // Assert
            _mockLogger.Verify(x => x.Log(LogLevel.Information,
                  It.IsAny<EventId>(),
                  It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Retailer Separation executed Successfully")),
                  It.IsAny<Exception>(),
                  It.IsAny<Func<It.IsAnyType, Exception, string>>()
              ),
              Times.Once);

        }

        [Fact]
        public async Task PerformSync_BaseProject_Exchange_ShouldExecute_DoCreate_Returns_Error()
        {
            // Arrange
            var eventArgs = new BasicDeliverEventArgs(
                "test-consumer",
                1,
                false,
                RetailerSeparationConstants.RetailerSeparationExchange,
                RMQConstants.RetailerSeparationProjectServicesRoutingKey,
                new Mock<IBasicProperties>().Object,
                new ReadOnlyMemory<byte>(Encoding.UTF8.GetBytes("{ \"Id\": \"c0a994e3-ca29-4a7a-a214-10ef28879a1e\", \"SyncingEntityId\": \"5\" }")),
                new CancellationToken()
            );

            _projectServicesHelper
                .Setup(client => client.PerformRetailerSeparation(It.IsAny<IRSeparationBaseProjectRequest>(), It.IsAny<string>()))
                .ThrowsAsync(new Exception("Simulated Exception")); 

            // Act
            await _msgSyncMock.PerformSync(eventArgs);

            // Assert
            _mockLogger.Verify(x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("EXCEPTION - PerformSync")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception, string>>()
            ), Times.Once);
        }

    }
}
