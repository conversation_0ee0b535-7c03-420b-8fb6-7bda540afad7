﻿using AutoFixture;
using BootstrapAPI.Core.Exception.Instances;
using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Infrastructure.Persistence.Entities;
using DWH.ProjectServices.API.Infrastructure.Persistence.Repositories.Interfaces;
using DWH.ProjectServices.API.Infrastructure.WebServiceClient;
using DWH.ProjectServices.API.Infrastructure.WebServiceClient.Interfaces;
using DWH.ProjectServices.API.Models;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;
using DWH.ProjectServices.API.Services.Constants;
using DWH.ProjectServices.API.Services.Helper;
using DWH.ProjectServices.API.Services.Helper.Interface;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Xunit;
using Xunit.Extensions.AssertExtensions;
using static DWH.ProjectServices.API.Services.Helper.Records.PeriodicityOperationRecords;

namespace DWH.ProjectServices.API.UnitTests.Services.Helper
{
    public class OperationHelperTests
    {
        private readonly Mock<IDateApiClient> _webClientMock;
        private readonly Mock<IAdministratorAPIClient> _adminClientMock;
        private readonly Mock<IQCPeriodRepository> _qcPeriodRepositoryMock;
        private readonly Mock<ILogger<OperationHelper>> _loggerMock;
        private readonly Mock<ITokenService> _tokenServiceMock;
        private readonly Mock<IBaseProjectRepository> _baseProjectRepositoryMock;
        private readonly Mock<IQCProjectRepository> _qcProjectRepositoryMock;
        private readonly OperationHelper _operationHelper;
        private readonly Fixture _fixture;

        public OperationHelperTests()
        {
            _webClientMock = new Mock<IDateApiClient>();
            _qcPeriodRepositoryMock = new Mock<IQCPeriodRepository>();
            _loggerMock = new Mock<ILogger<OperationHelper>>();
            _tokenServiceMock = new Mock<ITokenService>();
            _baseProjectRepositoryMock = new Mock<IBaseProjectRepository>();
            _qcProjectRepositoryMock = new Mock<IQCProjectRepository>();
            _adminClientMock = new Mock<IAdministratorAPIClient>();
            _fixture = new Fixture();

            _operationHelper = new OperationHelper(
                _qcPeriodRepositoryMock.Object,
                _loggerMock.Object,
                _tokenServiceMock.Object,
                _baseProjectRepositoryMock.Object,
                _qcProjectRepositoryMock.Object,
                _webClientMock.Object,
                _adminClientMock.Object
            );
        }

        [Fact]
        public async Task CalculateDistancesAsync_ShouldReturnCorrectDistances_WhenResponseIsSuccess()
        {
            // Arrange
            var token = _fixture.Create<TokenService.TokenResponse>();
            var periods = new List<Period>
            {
                new Period { index = 0, RefPeriodId = 1 }
            };

                    var periodId = 100;
                    var expectedDistances = new List<PeriodDistance>
            {
                new PeriodDistance(0, 5),
                new PeriodDistance(0, null), 
                new PeriodDistance(0, null),
                new PeriodDistance(0, null), 
                new PeriodDistance(0, null), 
                new PeriodDistance(0, null),
                new PeriodDistance(0, null)  
            };

            _tokenServiceMock.Setup(x => x.GetTokenAsync(It.IsAny<string>()))
                .ReturnsAsync(token);

            _webClientMock.Setup(x => x.GetAsync<List<DistanceResponse>>(It.IsAny<string>()))
              .ReturnsAsync(new ServiceResponse<List<DistanceResponse>>(
                  Data: new List<DistanceResponse>
                  {
                new DistanceResponse(1, 2, 5)
                  },
                  IsSuccess: true,
                  ErrorMessage: string.Empty,
                  StatusCode: HttpStatusCode.OK,
                  ReasonPhrase: "OK"
              ));

            // Act
            var result = await _operationHelper.CalculateDistancesAsync(periods, periodId);

            // Assert
            result.Should().BeEquivalentTo(expectedDistances);
        }

        [Fact]
        public async Task CalculateDistancesAsync_ShouldThrowArgumentNullException_WhenDistanceResponseIsNullOrEmpty()
        {
            // Arrange
            var token = _fixture.Create<TokenService.TokenResponse>();
            var periods = new List<Period>
            {
                new Period { index = 0, RefPeriodId = 1 }
            };

            var periodId = 100;

            _tokenServiceMock.Setup(x => x.GetTokenAsync(It.IsAny<string>()))
                .ReturnsAsync(token);

            _webClientMock.Setup(x => x.GetAsync<List<DistanceResponse>>(It.IsAny<string>()))
              .ReturnsAsync(new ServiceResponse<List<DistanceResponse>>(
                  Data: new List<DistanceResponse>(),  // Empty response
                  IsSuccess: true,
                  ErrorMessage: string.Empty,
                  StatusCode: HttpStatusCode.OK,
                  ReasonPhrase: "OK"
              ));

            // Act
            Func<Task> act = async () => await _operationHelper.CalculateDistancesAsync(periods, periodId);

            // Assert
            await act.Should().ThrowAsync<ArgumentNullException>()
                .WithMessage($"Value cannot be null. (Parameter 'Failed to parse distance value for RefPeriodId 1')");
        }

        [Fact]
        public async Task CalculateDistancesAsync_ShouldThrowHttpRequestException_WhenHttpRequestFails()
        {
            // Arrange
            var token = _fixture.Create<TokenService.TokenResponse>();
            var periods = new List<Period>
            {
                new Period { index = 0, RefPeriodId = 1 }
            };

            var periodId = 100;

            _tokenServiceMock.Setup(x => x.GetTokenAsync(It.IsAny<string>()))
                .ReturnsAsync(token);

            _webClientMock.Setup(x => x.GetAsync<List<DistanceResponse>>(It.IsAny<string>()))
              .ReturnsAsync(new ServiceResponse<List<DistanceResponse>>(
                  Data: null,  
                  IsSuccess: false,
                  ErrorMessage: "Something went wrong",
                  StatusCode: HttpStatusCode.BadRequest,
                  ReasonPhrase: "Bad Request"
              ));

            // Act
            Func<Task> act = async () => await _operationHelper.CalculateDistancesAsync(periods, periodId);

            // Assert
            await act.Should().ThrowAsync<HttpRequestException>()
                .WithMessage($"HTTP request failed: BadRequest (Bad Request) - Something went wrong");
        }

        [Fact]
        public async Task GetRecentQCPeriodsAsync_ShouldReturnFirstQCPeriod_WhenPeriodsExist()
        {
            // Arrange
            var qcProjectId = 1;
            var qcPeriods = new List<QCPeriod>
            {
                new QCPeriod { PeriodId = 1 },
                new QCPeriod { PeriodId = 2 }
            };

            _qcPeriodRepositoryMock.Setup(x => x.GetAllQCPeriodsAsync(qcProjectId))
                .ReturnsAsync(qcPeriods);

            // Act
            var result = await _operationHelper.GetRecentQCPeriodsAsync(qcProjectId);

            // Assert
            result.Should().Be(qcPeriods.First());
        }

        [Fact]
        public async Task GetRecentQCPeriodsAsync_ShouldThrowEntityNotExistsException_WhenNoQCPeriodsExist()
        {
            // Arrange
            var qcProjectId = 1;
            var qcPeriods = new List<QCPeriod>(); // No QC periods

            _qcPeriodRepositoryMock.Setup(x => x.GetAllQCPeriodsAsync(qcProjectId))
                .ReturnsAsync(qcPeriods);

            // Act
            Func<Task> act = async () => await _operationHelper.GetRecentQCPeriodsAsync(qcProjectId);

            // Assert
            await act.Should().ThrowAsync<EntityNotExistsException>()
                .WithMessage($"ENTITY_NOT_EXISTS");
        }


        [Fact]
        public async Task CalculateShiftedPeriodsAsync_ShouldReturnCorrectShiftedPeriods_WhenResponseIsSuccess()
        {
            // Arrange
            var token = _fixture.Create<TokenService.TokenResponse>();
            var targetPeriodId = 100;
            var distances = new List<PeriodDistance>
            {
                new PeriodDistance(0, 10),
                new PeriodDistance(1, 20),
                new PeriodDistance(2, null)
            };

            var expectedShiftedPeriods = new List<ShiftedPeriod>
            {
                new ShiftedPeriod(0, 1),
                new ShiftedPeriod(1, 2),
                new ShiftedPeriod(2, null)
            };

            _tokenServiceMock.Setup(x => x.GetTokenAsync(It.IsAny<string>()))
                .ReturnsAsync(token);

            _webClientMock.Setup(x => x.GetAsync<List<ShiftedPeriodResponse>>(It.IsAny<string>()))
              .ReturnsAsync(new ServiceResponse<List<ShiftedPeriodResponse>>(
                  Data: new List<ShiftedPeriodResponse>
                  {
                        new ShiftedPeriodResponse(1, "2", "5"),
                        new ShiftedPeriodResponse(2, "2", "5"),
                        new ShiftedPeriodResponse(3, "2", "5")
                  },
                  IsSuccess: true,
                  ErrorMessage: string.Empty,
                  StatusCode: HttpStatusCode.OK,
                  ReasonPhrase: "OK"
              ));

            // Act
            var result = await _operationHelper.CalculateShiftedPeriodsAsync(targetPeriodId, distances);

            // Assert
            result.Should().BeEquivalentTo(expectedShiftedPeriods);
        }

        [Fact]
        public async Task CalculateShiftedPeriodsAsync_ShouldThrowHttpRequestException_WhenResponseIsFailure()
        {
            // Arrange
            var token = _fixture.Create<TokenService.TokenResponse>();
            var targetPeriodId = 100;
            var distances = new List<PeriodDistance>
            {
                new PeriodDistance(0, 10),
                new PeriodDistance(1, 20),
                new PeriodDistance(2, null)
            };

            _tokenServiceMock.Setup(x => x.GetTokenAsync(It.IsAny<string>()))
                .ReturnsAsync(token);

            _webClientMock.Setup(x => x.GetAsync<List<ShiftedPeriodResponse>>(It.IsAny<string>()))
                .ReturnsAsync(new ServiceResponse<List<ShiftedPeriodResponse>>(
                    Data: null,
                    IsSuccess: false,
                    ErrorMessage: "Error retrieving shifted periods",
                    StatusCode: HttpStatusCode.InternalServerError,
                    ReasonPhrase: "Internal Server Error"
                ));

            // Act & Assert

            var exception = await _operationHelper.Invoking(s => s.CalculateShiftedPeriodsAsync(targetPeriodId, distances))
                                            .Should().ThrowAsync<HttpRequestException>();
            // Verify the exception message
            exception.NotBeNull();
            exception.Which.Message.Should().Be("Error calling API for shifted periods: InternalServerError (Internal Server Error) - Error retrieving shifted periods");
        }


        [Fact]
        public async Task GetBaseQCPeriodAsync_ShouldThrowEntityNotExistsException_WhenPeriodDoesNotExist()
        {
            // Arrange
            var qcProjectId = 1;
            var periodId = 100;
            var qcPeriods = new List<QCPeriod>();

            _qcPeriodRepositoryMock.Setup(x => x.GetAllQCPeriodsAsync(qcProjectId))
                .ReturnsAsync(qcPeriods);

            // Act
            Func<Task> act = async () => await _operationHelper.GetBaseQCPeriodAsync(qcProjectId, periodId);

            // Assert
            await act.Should().ThrowAsync<EntityNotExistsException>();
        }


        [Fact]
        public async Task GetBaseQCPeriodAsync_ShouldThrowEntityNotExistsException_WhenSpecificQCPeriodDoesNotExist()
        {
            // Arrange
            var qcProjectId = 1;
            var periodId = 100;
            var qcPeriods = new List<QCPeriod>
            {
                new QCPeriod { PeriodId = 1 },  // Example data
                new QCPeriod { PeriodId = 2 }   // Example data
            };

            _qcPeriodRepositoryMock.Setup(x => x.GetAllQCPeriodsAsync(qcProjectId))
                .ReturnsAsync(qcPeriods);

            // Act
            Func<Task> act = async () => await _operationHelper.GetBaseQCPeriodAsync(qcProjectId, periodId);

            // Assert
            var expectedErrorMessage = $"ENTITY_NOT_EXISTS";
            await act.Should().ThrowAsync<EntityNotExistsException>()
                .WithMessage(expectedErrorMessage);

        }

        [Fact]
        public async Task GetBaseQCPeriodAsync_ShouldReturnSpecificQCPeriod_WhenExists()
        {
            // Arrange
            var qcProjectId = 1;
            var periodId = 100;
            var expectedQCPeriod = new QCPeriod { PeriodId = periodId };

            var qcPeriods = new List<QCPeriod>
            {
                new QCPeriod { PeriodId = 1 },  // Example data
                new QCPeriod { PeriodId = 100 } // The periodId we are looking for
            };

            _qcPeriodRepositoryMock.Setup(x => x.GetAllQCPeriodsAsync(qcProjectId))
                .ReturnsAsync(qcPeriods);

            // Act
            var result = await _operationHelper.GetBaseQCPeriodAsync(qcProjectId, periodId);

            // Assert
            result.Should().BeEquivalentTo(expectedQCPeriod);
        }



        [Fact]
        public async Task GetPeriodShortNameAsync_ShouldReturnShortName_WhenResponseIsSuccess()
        {
            // Arrange
            var token = _fixture.Create<TokenService.TokenResponse>();
            var periodId = 100;
            var expectedShortName = "ShortName";

            _tokenServiceMock.Setup(x => x.GetTokenAsync(It.IsAny<string>()))
                .ReturnsAsync(token);

            _webClientMock.Setup(x => x.GetAsync<ShiftedPeriodResponse>(It.IsAny<string>()))
              .ReturnsAsync(new ServiceResponse<ShiftedPeriodResponse>(
                  Data: new ShiftedPeriodResponse(1, "2", "ShortName"),
                  IsSuccess: true,
                  ErrorMessage: string.Empty,
                  StatusCode: HttpStatusCode.OK,
                  ReasonPhrase: "OK"
              ));

            // Act
            var result = await _operationHelper.GetPeriodShortNameAsync(periodId);

            // Assert
            result.Should().Be(expectedShortName);
        }

        [Fact]
        public async Task GetPeriodShortNameAsync_ShouldThrowHttpRequestException_WhenResponseIsFailure()
        {
            // Arrange
            var token = _fixture.Create<TokenService.TokenResponse>();
            var periodId = 100;

            _tokenServiceMock.Setup(x => x.GetTokenAsync(AppConstants.DateAPI))
                .ReturnsAsync(token);

            _webClientMock.Setup(x => x.GetAsync<ShiftedPeriodResponse>(It.IsAny<string>()))
                .ReturnsAsync(new ServiceResponse<ShiftedPeriodResponse>(
                    Data: null,
                    IsSuccess: false,
                    ErrorMessage: "Error",
                    StatusCode: HttpStatusCode.BadRequest,
                    ReasonPhrase: "Bad Request"
                ));

            // Act & Assert
            var exception = await _operationHelper.Invoking(s => s.GetPeriodShortNameAsync(periodId))
                                            .Should().ThrowAsync<HttpRequestException>();

            // Verify the exception message
            exception.NotBeNull();
            exception.Which.Message.Should().Be("Error calling API for current periods: Status Code 400 (Bad Request)");
        }


        [Fact]
        public async Task GetPeriodRangeAsync_ShouldReturnPeriodIds_WhenResponseIsSuccess()
        {
            // Arrange
            var token = _fixture.Create<TokenService.TokenResponse>();
            var periodicityId = 1;
            var startPeriod = "2023-01";
            var endPeriod = "2023-12";
            var expectedIds = new List<long> { 1, 2, 3 };

            _tokenServiceMock.Setup(x => x.GetTokenAsync(It.IsAny<string>()))
                .ReturnsAsync(token);

            _webClientMock.Setup(x => x.GetAsync<List<ShiftedPeriodResponse>>(It.IsAny<string>()))
               .ReturnsAsync(new ServiceResponse<List<ShiftedPeriodResponse>>(
                   Data: new List<ShiftedPeriodResponse>
                   {
                        new ShiftedPeriodResponse(1, "2", "5"),
                        new ShiftedPeriodResponse(2, "2", "5"),
                        new ShiftedPeriodResponse(3, "2", "5")
                   },
                   IsSuccess: true,
                   ErrorMessage: string.Empty,
                   StatusCode: HttpStatusCode.OK,
                   ReasonPhrase: "OK"
               ));

            // Act
            var result = await _operationHelper.GetPeriodRangeAsync(periodicityId, startPeriod, endPeriod);

            // Assert
            result.Should().BeEquivalentTo(expectedIds);
        }

        [Fact]
        public async Task GetPeriodRangeAsync_ShouldThrowHttpRequestException_WhenResponseIsFailure()
        {
            // Arrange
            var token = _fixture.Create<TokenService.TokenResponse>();
            var periodicityId = 1;
            var startPeriod = "2023-01";
            var endPeriod = "2023-12";

            _tokenServiceMock.Setup(x => x.GetTokenAsync(It.IsAny<string>()))
                .ReturnsAsync(token);

            _webClientMock.Setup(x => x.GetAsync<List<ShiftedPeriodResponse>>(It.IsAny<string>()))
                .ReturnsAsync(new ServiceResponse<List<ShiftedPeriodResponse>>(
                    Data: null,
                    IsSuccess: false,
                    ErrorMessage: "Error retrieving periods",
                    StatusCode: HttpStatusCode.BadRequest,
                    ReasonPhrase: "Bad Request"
                ));

            // Act & Assert

            var exception = await _operationHelper.Invoking(s => s.GetPeriodRangeAsync(periodicityId, startPeriod, endPeriod))
                                            .Should().ThrowAsync<HttpRequestException>();

            // Verify the exception message
            exception.NotBeNull();
            exception.Which.Message.Should().Be("Error calling API for current periods: Status Code 400 (Bad Request)");
        }


        [Fact]
        public async Task GetPeriodicityIdAsync_ShouldReturnPeriodicityId_WhenBaseProjectExists()
        {
            // Arrange
            var qcProjectId = 1;
            var expectedPeriodicityId = 5;

            _baseProjectRepositoryMock.Setup(x => x.GetBaseProjectAsync(qcProjectId))
                .ReturnsAsync(new BaseProject { PeriodicityId = expectedPeriodicityId });

            // Act
            var result = await _operationHelper.GetPeriodicityIdAsync(qcProjectId);

            // Assert
            result.Should().Be(expectedPeriodicityId);
        }

        [Fact]
        public async Task ExtractAndFormatEndDateAsync_ShouldReturnFormattedDate_WhenDateIsValid()
        {
            // Arrange
            var name = "ProjectName -(01.12.2023)";
            var expectedDate = "2023-12-01";

            // Act
            var result = await _operationHelper.ExtractAndFormatEndDateAsync(name);

            // Assert
            result.Should().Be(expectedDate);
        }

        [Fact]
        public async Task ParseAndFormatDateAsync_ShouldReturnFormattedDate_WhenDateIsValid()
        {
            // Arrange
            var dateString = "01.12.2023";
            var expectedDate = "2023-12-01";

            // Act
            var result = await _operationHelper.ParseAndFormatDateAsync(dateString);

            // Assert
            result.Should().Be(expectedDate);
        }

        [Fact]
        public async Task ExtractAndFormatEndDateAsync_ShouldThrowFormatException_WhenDateIsInvalid()
        {
            // Arrange
            var name = "ProjectName - (InvalidDate)";

            // Act
            Func<Task> act = async () => await _operationHelper.ExtractAndFormatEndDateAsync(name);

            // Assert
            await act.Should().ThrowAsync<FormatException>();
                    
        }


        [Fact]
        public async Task GetPeriodNameAsync_ShouldReturnPeriodName_WhenResponseIsSuccess()
        {
            // Arrange
            var token = _fixture.Create<TokenService.TokenResponse>();
            var periodId = 100;
            var expectedName = "Sample Period";
            _tokenServiceMock.Setup(x => x.GetTokenAsync(AppConstants.DateAPI))
                .ReturnsAsync(token);

            _webClientMock.Setup(x => x.GetAsync<ShiftedPeriodResponse>(It.IsAny<string>()))
             .ReturnsAsync(new ServiceResponse<ShiftedPeriodResponse>(
                 Data: new ShiftedPeriodResponse(1, "Sample Period", "Sample Period Short"),
                 IsSuccess: true,
                 ErrorMessage: string.Empty,
                 StatusCode: HttpStatusCode.OK,
                 ReasonPhrase: "OK"
             ));

            // Act
            var result = await _operationHelper.GetPeriodNameAsync(periodId);

            // Assert
            result.Should().Be(expectedName);
        }

        [Fact]
        public async Task GetPeriodNameAsync_ShouldThrowHttpRequestException_WhenResponseIsFailure()
        {
            // Arrange
            var token = _fixture.Create<TokenService.TokenResponse>();
            var periodId = 100;
            _tokenServiceMock.Setup(x => x.GetTokenAsync(AppConstants.DateAPI))
                .ReturnsAsync(token);

            _webClientMock.Setup(x => x.GetAsync<ShiftedPeriodResponse>($"/api/v1/Periods/{periodId}"))
                .ReturnsAsync(new ServiceResponse<ShiftedPeriodResponse>(
                    Data: null,
                    IsSuccess: false,
                    ErrorMessage: "Error",
                    StatusCode: HttpStatusCode.BadRequest,
                    ReasonPhrase: "Bad Request"
                ));

            // Act
            Func<Task> act = async () => await _operationHelper.GetPeriodNameAsync(periodId);

            // Assert
            await act.Should().ThrowAsync<HttpRequestException>();
                    
        }


        [Fact]
        public async Task ExtractAndFormatEndDateAsyncMonthy_ShouldReturnFormattedDate_WhenDateIsValidWithParentheses()
        {
            // Arrange
            var name = "ProjectName -(01.12.2023)";
            var expectedDate = "2023-12-01";

            // Act
            var result = await _operationHelper.ExtractAndFormatEndDateAsyncMonthy(name);

            // Assert
            result.Should().Be(expectedDate);
        }

        [Fact]
        public async Task ExtractAndFormatEndDateAsyncMonthy_ShouldThrowArgumentException_WhenDateIsValidWithWrongParentheses()
        {
            // Arrange
            var name = "ProjectName -)(01.12.2023";

            // Act
            Func<Task> act = async () => await _operationHelper.ExtractAndFormatEndDateAsyncMonthy(name);

            // Assert
            await act.Should().ThrowAsync<ArgumentException>();
                  
        }

        [Fact]
        public async Task ExtractAndFormatEndDateAsyncMonthy_ShouldThrowArgumentException_WhenDateIsValidWithUnrecognizedParentheses()
        {
            // Arrange
            var name = "ProjectName 01.12.2023";

            // Act
            Func<Task> act = async () => await _operationHelper.ExtractAndFormatEndDateAsyncMonthy(name);

            // Assert
            await act.Should().ThrowAsync<ArgumentException>();
                    
        }


        [Fact]
        public async Task ExtractAndFormatEndDateAsyncMonthy_ShouldReturnFormattedDate_WhenDateIsValidWithoutParentheses()
        {
            // Arrange
            var name = "ProjectName - December 2023";
            var expectedDate = "2023-12-31";

            // Act
            var result = await _operationHelper.ExtractAndFormatEndDateAsyncMonthy(name);

            // Assert
            result.Should().Be(expectedDate);
        }
        [Fact]
        public async Task ExtractAndFormatEndDateAsyncMonthy_ShouldThrowFormatException_WhenDateIsInvalidInStringWithoutParentheses()
        {
            // Arrange
            var name = "ProjectName - InvalidMonth";

            // Act
            Func<Task> act = async () => await _operationHelper.ExtractAndFormatEndDateAsyncMonthy(name);

            // Assert
            await act.Should().ThrowAsync<FormatException>();
                     
        }

        [Fact]
        public async Task ExtractAndFormatEndDateAsyncMonthy_ShouldThrowFormatExceptionInside_WhenDateIsInvalidInStringWithoutParentheses()
        {
            // Arrange
            var name = "ProjectName - (01.13.2023)";

            // Act
            Func<Task> act = async () => await _operationHelper.ExtractAndFormatEndDateAsyncMonthy(name);

            // Assert
            await act.Should().ThrowAsync<FormatException>();
                    
        }


        [Fact]
        public async Task GetPeriodsAsync_ShouldReturnPeriods_WhenResponseIsSuccess()
        {
            // Arrange
            var token = _fixture.Create<TokenService.TokenResponse>();
            var periodicityId = 1;
            var expectedPeriods = new List<ShiftedPeriodResponse>
            {
                new ShiftedPeriodResponse(1, "2", "5"),
                new ShiftedPeriodResponse(2, "2", "5"),
                new ShiftedPeriodResponse(3, "2", "5")
            };

            _tokenServiceMock.Setup(x => x.GetTokenAsync(AppConstants.DateAPI))
                .ReturnsAsync(token);

            _webClientMock.Setup(x => x.GetAsync<List<ShiftedPeriodResponse>>(
                    $"/api/v1/Periods/current?periodicityId={periodicityId}"))
                    .ReturnsAsync(new ServiceResponse<List<ShiftedPeriodResponse>>(
                        Data: expectedPeriods,
                        IsSuccess: true,
                        ErrorMessage: string.Empty,
                        StatusCode: HttpStatusCode.OK,
                        ReasonPhrase: "OK"
                    ));

            // Act
            var result = await _operationHelper.GetPeriodsAsync(periodicityId);

            // Assert
            result.Should().BeEquivalentTo(expectedPeriods);
        }

        [Fact]
        public async Task GetPeriodsAsync_ShouldThrowHttpRequestException_WhenResponseIsFailure()
        {
            // Arrange
            var token = _fixture.Create<TokenService.TokenResponse>();
            var periodicityId = 1;
            _tokenServiceMock.Setup(x => x.GetTokenAsync(AppConstants.DateAPI))
                .ReturnsAsync(token);

            _webClientMock.Setup(x => x.GetAsync<List<ShiftedPeriodResponse>>($"/api/v1/Periods/current?periodicityId={periodicityId}"))
                .ReturnsAsync(new ServiceResponse<List<ShiftedPeriodResponse>>(
                    Data: null,
                    IsSuccess: false,
                    ErrorMessage: "Error",
                    StatusCode: HttpStatusCode.BadRequest,
                    ReasonPhrase: "Bad Request"
                ));

            // Act
            Func<Task> act = async () => await _operationHelper.GetPeriodsAsync(periodicityId);

            // Assert
            await act.Should().ThrowAsync<HttpRequestException>()
                .WithMessage("*Bad Request*");
        }




        [Fact]
        public async Task GetBCRDetails_ShouldReturnBCRDetails_WhenResponseIsSuccess()
        {
            // Arrange
            var token = _fixture.Create<TokenService.TokenResponse>();
            var endpoint = "/api/v1/BCR";
            var productGroups = new[] { 1, 2, 3 };
            var expectedDetails = new BaseChannelRearrangements { NumberOfConflicts = 2 };

            _tokenServiceMock.Setup(x => x.GetTokenAsync(AppConstants.AdminstratorAPI))
                .ReturnsAsync(token);

            _adminClientMock.Setup(x => x.PostAsync<BaseChannelRearrangements>(endpoint, It.IsAny<StringContent>()))
                .ReturnsAsync(new ServiceResponse<BaseChannelRearrangements>(
                    Data: expectedDetails,
                    IsSuccess: true,
                    ErrorMessage: string.Empty,
                    StatusCode: HttpStatusCode.OK,
                    ReasonPhrase: "OK"
                ));

            // Act
            var result = await _operationHelper.GetBCRDetails(endpoint, productGroups);

            // Assert
            result.Should().BeEquivalentTo(expectedDetails);
        }

        [Fact]
        public async Task GetBCRDetails_ShouldThrowHttpRequestException_WhenResponseIsFailure()
        {
            // Arrange
            var token = _fixture.Create<TokenService.TokenResponse>();
            var endpoint = "/api/v1/BCR";
            var productGroups = new[] { 1, 2, 3 };
            var expectedDetails = new BaseChannelRearrangements { NumberOfConflicts = 0 };

            _tokenServiceMock.Setup(x => x.GetTokenAsync(AppConstants.AdminstratorAPI))
                .ReturnsAsync(token);

            _adminClientMock.Setup(x => x.PostAsync<BaseChannelRearrangements>(endpoint, It.IsAny<StringContent>()))
                .ReturnsAsync(new ServiceResponse<BaseChannelRearrangements>(
                    Data: expectedDetails,
                    IsSuccess: false,
                    ErrorMessage: "Error",
                    StatusCode: HttpStatusCode.BadRequest,
                    ReasonPhrase: "Bad Request"
                ));

            // Act & Assert
            var exception = await _operationHelper.Invoking(s => s.GetBCRDetails(endpoint, productGroups))
                                            .Should().ThrowAsync<HttpRequestException>();

            // Optionally, check the message of the exception to ensure it contains expected details
            exception.NotBeNull();
            exception.Which.Message.Should().Contain("Error calling API for current productGroups: Status Code 400 (Bad Request) - NumberOfConflicts: 0");
        }






    }
}
