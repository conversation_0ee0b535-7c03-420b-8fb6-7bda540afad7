﻿using AutoFixture;
using FluentAssertions;
using DWH.ProjectServices.API.Models;
using DWH.ProjectServices.API.Services;
using Moq;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Xunit;
using DWH.ProjectServices.API.Infrastructure.Persistence.Repositories.Interfaces;
using BootstrapAPI.Core.Exception.Instances;
using DWH.ProjectServices.API.Domain.Models;

namespace DWH.ProjectServices.API.UnitTests.Services
{
    public class SubProjectTypeServiceUTest
    {
        private readonly IFixture _fixture;
        private readonly Mock<IProjectSubTypeRepository> _projectSubTypeRepositoryStub;
        private readonly ProjectSubTypeService _service;

        public SubProjectTypeServiceUTest()
        {
            _fixture = new Fixture();
            _projectSubTypeRepositoryStub = new Mock<IProjectSubTypeRepository>();
            _service = new ProjectSubTypeService(_projectSubTypeRepositoryStub.Object);
        }

        [Fact]
        public async Task GetAllAsync_When_SubProjectTypesNotExist_Expect_EmptyList()
        {
            // Arrange
            var expectedResult = Array.Empty<ProjectSubTypes>();

            _projectSubTypeRepositoryStub
                .Setup(pr => pr.FindAllAsync())
                .ReturnsAsync((expectedResult));

            // Act
            var result = await _service.GetAllAsync();

            // Assert
            result.Should().NotBeNull().And.BeEquivalentTo(expectedResult);
        }



        [Fact]
        public async Task GetAllAsync_When_SubProjectTypesExist_Expect_ListOfSubProjectTypes()
        {
            // Arrange
            var expectedResult = _fixture.Create<IReadOnlyCollection<ProjectSubTypes>>();

            _projectSubTypeRepositoryStub
                .Setup(pr => pr.FindAllAsync())
                .ReturnsAsync(expectedResult);

            // Act
            var result = await _service.GetAllAsync();

            // Assert
            result.Should().BeEquivalentTo(expectedResult);
        }

        [Fact]
        public async Task GetAllAsync_WhenCalled_AndRepositoryThrowsException_ThrowsException()
        {
            // Arrange
            _projectSubTypeRepositoryStub
                .Setup(repo => repo.FindAllAsync())
                .ThrowsAsync(new EntityNotExistsException($"ProjectSubType Not Found "));

            // Act
            var act = async () => await _service.GetAllAsync();

            // Assert
            await act.Should().ThrowAsync<EntityNotExistsException>();
        }


    }
}
