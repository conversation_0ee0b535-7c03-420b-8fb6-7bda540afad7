﻿using Xunit;
using Moq;
using AutoFixture;
using Microsoft.Extensions.Logging;
using System;
using DWH.ProjectServices.API.Models;
using RabbitMQ.Client;
using AutoMapper;
using DWH.ProjectServices.API.Models.Dtos;
using DWH.ProjectServices.API.Services;
using Microsoft.AspNetCore.Http;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ.Constants;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ.Interfaces;
using FluentAssertions;
using DWH.ProjectServices.API.Models.Interfaces;

namespace RabbitMQ.Tests
{
    public class RabbitMQSenderTests
    {
        private readonly Fixture _fixture;
        private readonly Mock<IRabbitMQConnectionFactory> _mockConnectionFactory;
        private readonly Mock<IConnection> _mockConnection;
        private readonly Mock<IChannel> _mockChannel;
        private readonly Mock<ILogger<RabbitMQSender>> _mockLogger;
        private readonly RabbitMQSender _rabbitMQSender;

        public RabbitMQSenderTests()
        {
            _fixture = new Fixture();

            // Mocks
            _mockConnectionFactory = new Mock<IRabbitMQConnectionFactory>();
            _mockConnection = new Mock<IConnection>();
            _mockChannel = new Mock<IChannel>();
            _mockLogger = new Mock<ILogger<RabbitMQSender>>();

            _mockConnectionFactory.Setup(x => x.CreateConnectionAsync())
                .ReturnsAsync(_mockConnection.Object);
            _mockConnection.Setup(x => x.CreateChannelAsync(null, It.IsAny<CancellationToken>()))
                .ReturnsAsync(_mockChannel.Object);

            _rabbitMQSender = new RabbitMQSender(_mockConnectionFactory.Object, _mockLogger.Object);
        }


        [Fact]
        public void SendToRabbitMQ_NullData_ThrowsArgumentNullException()
        {
            // Arrange
            string exchangeName = QCPeriodConstants.QCPeriodExchange;
            string routingKey = QCPeriodConstants.CreateRoutingKey;

            // Act & Assert
            _rabbitMQSender.Invoking(sender => sender.SendToRabbitMQ(exchangeName, routingKey, null))
                .Should().ThrowAsync<ArgumentNullException>()
                .WithMessage("Value cannot be null. (Parameter 'data')"); // Adjust the message based on the actual parameter name in your method
        }

        [Fact]
        public void SendToRabbitMQ_NullSyncingEntityId_ThrowsArgumentNullException()
        {
            // Arrange
            string exchangeName = QCPeriodConstants.QCPeriodExchange;
            string routingKey = QCPeriodConstants.CreateRoutingKey;

            var dataWithNullSyncingEntityId = new ProjectServicesData
            {
                Id = Guid.NewGuid(),
            };

            // Act & Assert
            _rabbitMQSender.Invoking(sender => sender.SendToRabbitMQ(exchangeName, routingKey, dataWithNullSyncingEntityId))
                .Should().ThrowAsync<ArgumentNullException>()
                .WithMessage("Value cannot be null. (Parameter 'data')"); // Adjust the message based on your implementation
        }

        [Fact]
        public void SendToRabbitMQ_EmptyMessageBody_ThrowsArgumentNullException()
        {
            // Arrange
            string exchangeName = QCPeriodConstants.QCPeriodExchange;
            string routingKey = QCPeriodConstants.CreateRoutingKey;

            // Act & Assert
            _rabbitMQSender.Invoking(sender => sender.SendToRabbitMQ(exchangeName, routingKey, new ProjectServicesData()))
                .Should().ThrowAsync<ArgumentNullException>()
                .WithMessage("Value cannot be null. (Parameter 'data')"); // Adjust the message based on your implementation
        }

        [Fact]
        public void Constructor_ShouldThrowArgumentNullException_WhenConnectionFactoryIsNull()
        {
            Assert.Throws<ArgumentNullException>(() =>
                new RabbitMQSender(null, _mockLogger.Object));
        }

        [Fact]
        public void Constructor_Should_ThrowException_When_ConnectionFactory_Is_Null()
        {
            Assert.Throws<ArgumentNullException>(() => new RabbitMQSender(null, _mockLogger.Object));
        }

        [Fact]
        public void SendToRabbitMQ_Should_ThrowException_When_ExchangeName_Is_NullOrEmpty()
        {
            var data = new Mock<IProjectServicesData>().Object;

            Assert.ThrowsAsync<ArgumentNullException>(() => _rabbitMQSender.SendToRabbitMQ(null, "routingKey", data));
            Assert.ThrowsAsync<ArgumentException>(() => _rabbitMQSender.SendToRabbitMQ("", "routingKey", data));
        }

        [Fact]
        public void SendToRabbitMQ_Should_ThrowException_When_RoutingKey_Is_NullOrEmpty()
        {
            var data = new Mock<IProjectServicesData>().Object;

            Assert.ThrowsAsync<ArgumentNullException>(() => _rabbitMQSender.SendToRabbitMQ("exchange", null, data));
            Assert.ThrowsAsync<ArgumentException>(() => _rabbitMQSender.SendToRabbitMQ("exchange", "", data));
        }

        [Fact]
        public void SendToRabbitMQ_ShouldThrowArgumentNullException_WhenDataIsNull()
        {
            Assert.ThrowsAsync<ArgumentNullException>(() =>
                _rabbitMQSender.SendToRabbitMQ("exchange", "routingKey", null));
        }

        [Fact]
        public void SendToRabbitMQ_Should_ThrowException_When_Data_SyncingEntityId_Is_Null()
        {
            var mockData = new Mock<IProjectServicesData>();
            mockData.SetupGet(d => d.SyncingEntityId).Returns((string)null);

            Assert.ThrowsAsync<ArgumentNullException>(()
                => _rabbitMQSender.SendToRabbitMQ("exchange", "routingKey", mockData.Object));
        }

        [Fact]
        public void SendToRabbitMQ_ShouldLogInformation_WhenMessageIsSent()
        {
            var data = new Mock<IProjectServicesData>();
            data.Setup(d => d.SyncingEntityId).Returns("test_id");

            _rabbitMQSender.SendToRabbitMQ("exchange", "routingKey", data.Object);

            _mockLogger.Verify(logger => logger.Log(
                                LogLevel.Information,
                                It.IsAny<EventId>(),
                                It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Message sent to RabbitMQ")),
                                null,
                                It.IsAny<Func<It.IsAnyType, Exception?, string>>()), Times.Once);
        }

        [Fact]
        public async Task DisposeAsync_ShouldCloseConnectionAndChannel()
        {
            await _rabbitMQSender.DisposeAsync();

            _mockChannel.Verify(channel => channel.CloseAsync(It.IsAny<ushort>(), It.IsAny<string>(), It.IsAny<bool>(), It.IsAny<CancellationToken>()), Times.Once);
            _mockConnection.Verify(connection => connection.CloseAsync(It.IsAny<ushort>(), It.IsAny<string>(), It.IsAny<TimeSpan>(), It.IsAny<bool>(), It.IsAny<CancellationToken>()), Times.Once);
        }
    }
}





