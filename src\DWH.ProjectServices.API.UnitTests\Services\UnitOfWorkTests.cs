﻿//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Text;
//using System.Threading.Tasks;
//using DWH.ProjectServices.API.Infrastructure.Persistence;
//using DWH.ProjectServices.API.Services.Helper;
//using FluentAssertions;
//using Moq;

//namespace DWH.ProjectServices.API.UnitTests.Services
//{
//    public class UnitOfWorkTests
//    {
//        private readonly Mock<PostgreSqlDbContext> _mockDbContext;
//        private readonly UnitOfWork _unitOfWork;

//        public UnitOfWorkTests()
//        {
//            _mockDbContext = new Mock<PostgreSqlDbContext>();
//            _unitOfWork = new UnitOfWork(_mockDbContext.Object);
//        }

//        [Fact]
//        public void BeginTransaction_Should_Set_IsUnitOfWorkActive_To_True()
//        {
//            // Act
//            _unitOfWork.BeginTransaction();

//            // Assert
//            _unitOfWork.IsUnitOfWorkActive.Should().BeTrue();
//        }

//        [Fact]
//        public async Task SaveChangesAsync_Should_Call_SaveChangesAsync_Once_When_UoW_Is_Active()
//        {
//            // Arrange
//            _unitOfWork.BeginTransaction();

//            // Act
//            await _unitOfWork.SaveChangesAsync();

//            // Assert
//            _mockDbContext.Verify(db => db.SaveChangesAsync(default), Times.Once);
//            _unitOfWork.IsUnitOfWorkActive.Should().BeFalse();
//        }

//        [Fact]
//        public async Task SaveChangesAsync_Should_Not_Call_SaveChangesAsync_When_UoW_Is_Not_Active()
//        {
//            // Act
//            await _unitOfWork.SaveChangesAsync();

//            // Assert
//            _mockDbContext.Verify(db => db.SaveChangesAsync(default), Times.Never);
//        }

//        [Fact]
//        public async Task SaveChangesAsync_Should_Reset_IsUnitOfWorkActive_To_False_After_Committing()
//        {
//            // Arrange
//            _unitOfWork.BeginTransaction();

//            // Act
//            await _unitOfWork.SaveChangesAsync();

//            // Assert
//            _unitOfWork.IsUnitOfWorkActive.Should().BeFalse();
//        }

//        [Fact]
//        public async Task SaveChangesAsync_Should_Handle_Exception_And_Not_Reset_IsUnitOfWorkActive_If_Failed()
//        {
//            // Arrange
//            _unitOfWork.BeginTransaction();
//            _mockDbContext.Setup(db => db.SaveChangesAsync(default)).ThrowsAsync(new System.Exception("Database failure"));

//            // Act
//            var act = async () => await _unitOfWork.SaveChangesAsync();

//            // Assert
//            await act.Should().ThrowAsync<System.Exception>().WithMessage("Database failure");
//            _unitOfWork.IsUnitOfWorkActive.Should().BeTrue(); // Should remain active in case of failure
//        }
//    }
//}

