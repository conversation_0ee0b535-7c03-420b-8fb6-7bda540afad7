﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<GenerateDocumentationFile>true</GenerateDocumentationFile>

		<Nullable>disable</Nullable>

		<OutputType>Exe</OutputType>
	</PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
		<NoWarn>1701;1702;1591</NoWarn>
	</PropertyGroup>
	<PropertyGroup>
		<ErrorOnDuplicatePublishOutputFiles>false</ErrorOnDuplicatePublishOutputFiles>
	</PropertyGroup>
	<PropertyGroup>
		<GenerateDocumentationFile>true</GenerateDocumentationFile>
		<TargetFramework>net8.0</TargetFramework>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.0" />
		<PackageReference Include="BootstrapAPI" Version="1.6.1" />
		<PackageReference Include="Elastic.Apm.NetCoreAll" Version="1.17.0" />
		<PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.7" />
		<PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.11" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.11">
		  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		  <PrivateAssets>all</PrivateAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="8.0.11" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.11">
		  <PrivateAssets>all</PrivateAssets>
		  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore" Version="8.0.11" />
		<PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.11" />

		<PackageReference Include="OpenTelemetry.Instrumentation.EntityFrameworkCore" Version="1.0.0-beta.6" />
		<PackageReference Include="Oracle.EntityFrameworkCore" Version="8.23.60" />
		<PackageReference Include="Polly" Version="8.4.1" />
		<PackageReference Include="prometheus-net.AspNetCore" Version="8.2.1" />
		<PackageReference Include="RabbitMQ.Client" Version="7.1.0" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
	</ItemGroup>

	<ProjectExtensions>
		<VisualStudio>
			<UserProperties />
		</VisualStudio>
	</ProjectExtensions>

</Project>

