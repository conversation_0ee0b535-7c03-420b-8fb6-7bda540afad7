﻿namespace DWH.ProjectServices.API.Domain.Enum
{
    public enum PeriodicityType
    {
        Daily = 1,
        Weekly = 2,
        Monthly = 4,
        TwoMonthlyStartJan = 5,
        FourMont<PERSON>StartJan = 6,
        YearlyStartJan = 7,
        TwoMonthlyStartFeb = 8,
        FourMonthlyStartFeb = 9,
        YearlyStartFeb = 16,
        ThreeMonthlyStartJan = 17,
        NineMonthlyStartApr = 18,
        SixMonthlyStartJan = 19,
        FourMonthlyStartMar = 21,
        SixMonthlyStartMar = 22,
        FourMonthlyStartApr = 23,
        SevenMonthlyStartJan = 24,
        FiveMonthlyStartAug = 25,
        SixMonthlyStartFeb = 26,
        ThreeMonthlyStartFeb = 27,
        FiveM<PERSON><PERSON>StartJan = 28,
        EightMonthlyStartJan = 29,
        Nine<PERSON>onthlyStartJan = 30,
        Ten<PERSON><PERSON>hlyStartJan = 31,
        ElevenMonthlyStartJan = 32,
        SixMonthlyStartApr = 33,
        ThreeMonthlyStartMar = 34
    }

}
