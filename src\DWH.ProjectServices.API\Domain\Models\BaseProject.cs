﻿


using System.ComponentModel.DataAnnotations.Schema;

namespace DWH.ProjectServices.API.Domain.Models
{
    public class BaseProject
    {
        
        public int Id { get; set; }     
        public string Name { get; set; }
        public int TypeId { get; set; }

        public int PanelId { get; set; }
        public int DataTypeId { get; set; }
        public int PurposeId { get; set; }
        public int PeriodicityId { get; set; }
        public int CountryId { get; set; }
        public bool IsRelevantForReportingEnabled { get; set; }
        public string CreatedBy { get; set; }
        public DateTimeOffset CreatedWhen { get; set; }
        public string? UpdatedBy { get; set; }
        public DateTimeOffset? UpdatedWhen { get; set; }
        public string? DeletedBy { get; set; }
        public DateTimeOffset? DeletedWhen { get; set; }
        public bool? Deleted { get; set; }

        public ICollection<BaseProjectProductGroup> ProductGroups { get; set; }
        public ICollection<BaseProjectPredecessor> Predecessors { get; set; }
        public QCProject QCProjects { get; set; }
        public DataTypes DataType { get; set; }
        public Purposes Purpose { get; set; }

        public bool IsRetailerSeparationRequested { get; set; }
        [NotMapped]
        public int ProductGroupCount => ProductGroups?.Count ?? 0;


    }
}
