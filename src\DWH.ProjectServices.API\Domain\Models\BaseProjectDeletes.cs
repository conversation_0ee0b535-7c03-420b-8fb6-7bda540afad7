﻿namespace DWH.ProjectServices.API.Domain.Models
{
    public class BaseProjectDeletes
    {
        public List<int> Ids { get; private set; }
        public string DeletedBy { get; private set; }
        public string UserName { get; private set; }

        public BaseProjectDeletes(List<int> ids, string deletedBy, string userName)
        {
            Ids = ids ?? throw new ArgumentNullException(nameof(ids));
            if (!Ids.Any())
            {
                throw new ArgumentException("Must have at least one id to proceed", nameof(ids));
            }
            DeletedBy = deletedBy;
            UserName = userName;
        }
    }
}