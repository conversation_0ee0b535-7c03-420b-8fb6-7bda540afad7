﻿namespace DWH.ProjectServices.API.Domain.Models
{
    public class BaseProjectsLists
    {

        public int Id { get; set; }
        public string Name { get; set; }
        public int[] CountryIds { get; set; }
        public long[] ProductGroupIds { get; set; }
        public int[] PeriodicityIds { get; set; }
        public long[] PanelIds { get; set; }
        public long[] DataTypeIds { get; set; }
        public long[] PurposeIds { get; set; }
        public int[] TypeIds { get; set; }
        public int Limit { get; set; }
        public string[] Usernames { get; set; }
        public DateTime? StartingDate { get; set; }
        public DateTime? EndingDate { get; set; }
        public int[] BaseProjectIDs { get; set; }
        public int[] QCProjectIDs { get; set; }

        public bool Deleted { get; set; }

    }
}

