﻿

namespace DWH.ProjectServices.API.Domain.Models
{
    public class Dependencies
    {
        public Dependencies(string baseProjectId, string qcProjectId, int statusCode, string statusMsg, ProjectsDependencies dependency)
        {
            BaseProjectId = baseProjectId;
            QCProjectId = qcProjectId;
            StatusCode = statusCode;
            StatusMsg = statusMsg;
            Dependency = dependency;
        }
        public string BaseProjectId { get; set; }
        public string QCProjectId { get; set; }

        public int StatusCode { get; set; }
        public string StatusMsg { get; set; }

        public ProjectsDependencies Dependency { get; set; }
    }
}
