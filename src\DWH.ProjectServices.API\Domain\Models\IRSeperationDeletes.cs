﻿using System.ComponentModel.DataAnnotations;

namespace DWH.ProjectServices.API.Domain.Models
{
    public class IRSeperationDeletes : IValidatableObject
    {
        [Required]
        public List<int> RetailerSeperationIds { get; set; }
        public string Message { get; set; }
        public string Email { get; set; }

        public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
        {
            var results = new List<ValidationResult>();
            if (RetailerSeperationIds.Count <= 0)
            {
                results.Add(new ValidationResult("Must have atleast one id to proceed"));
            }

            return results;
        }
    }
}
