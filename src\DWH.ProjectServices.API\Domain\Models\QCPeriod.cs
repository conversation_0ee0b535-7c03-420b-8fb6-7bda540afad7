﻿

namespace DWH.ProjectServices.API.Domain.Models
{
    public class QCPeriod
    {
        public long Id { get; set; }
        public int QCProjectId { get; set; }
        public long PeriodId { get; set; }
        public int? Status { get; set; }
        public string CreatedBy { get; set; }
        public DateTimeOffset CreatedWhen { get; set; }
        public string UpdatedBy { get; set; }
        public DateTimeOffset? UpdatedWhen { get; set; }
        public ICollection<Period> Periods { get; set; }
        public StockInitialization StockInitialization { get; set; }
    }
}
