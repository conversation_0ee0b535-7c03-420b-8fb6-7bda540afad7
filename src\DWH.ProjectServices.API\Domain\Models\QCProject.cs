﻿
namespace DWH.ProjectServices.API.Domain.Models
{
    public class QCProject
    {
        public int Id { get; set; }
        public int BaseProjectId { get; set; }
        public int? ResetCorrectionTypeId { get; set; }

        public bool? IsAutoLoad { get; set; }

        public int? SQCMode { get; set; }

        public bool? IsAutomatedPriceCheck { get; set; }
        public ICollection<QCPeriod> QCPeriods { get; set; }
    }
}
