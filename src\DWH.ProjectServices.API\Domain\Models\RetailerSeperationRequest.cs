﻿using DWH.ProjectServices.API.Infrastructure.Persistence.Entities;
using System.ComponentModel.DataAnnotations;

namespace DWH.ProjectServices.API.Domain.Models
{
    public class RetailerSeperationRequest
    {
        public int Id { get; set; }

        public long FromPeriodId { get; set; }

        public long ToPeriodId { get; set; }

        public int RequestStatusId { get; set; }

        public bool ResetCorrection { get; set; }

        public bool Extrapolation { get; set; }

        public string RequestedBy { get; set; }

        public DateTimeOffset RequestedWhen { get; set; }

        public string JiraId { get; set; }

        public ICollection<RetailerSeperation> RetailerSeperations { get; set; }

        public ICollection<RetailerSeperationRequestDetail> RetailerSeperationRequestDetails { get; set; }
    }
}
