﻿using System.Text.Json.Serialization;

namespace DWH.ProjectServices.API.Domain.Models
{
    public class RetailerSeperationRequestDetail
    {
        public int Id { get; set; }

        public int RetailerSeperationRequestId { get; set; }
        public string UpdatedBy { get; set; }
        public DateTimeOffset UpdatedWhen { get; set; }

        [JsonIgnore]
        public int RequestStatusId { get; set; }

        public string RequestStatus { get; set; }
        
    }
}
