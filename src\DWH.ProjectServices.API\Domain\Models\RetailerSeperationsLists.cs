﻿namespace DWH.ProjectServices.API.Domain.Models
{
    public class RetailerSeperationsLists //NOSONAR
    {
        public int[] Ids { get; set; }
        public long[] FromPeriodIds { get; set; }
        public long[] ToPeriodIds { get; set; }
        public bool[] ResetCorrections { get; set; }
        public bool[] Extrapolations { get; set; }
        public string JiraIds { get; set; }
        public int[] RequestStatusIds { get; set; }
        public int[] SourceBPIds { get; set; }
        public int[] RetailerBPIds { get; set; }
        public string[] Usernames { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }

    }
}
