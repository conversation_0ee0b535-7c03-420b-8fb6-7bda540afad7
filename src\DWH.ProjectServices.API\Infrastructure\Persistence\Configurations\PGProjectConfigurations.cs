﻿using DWH.ProjectServices.API.Models;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;

namespace DWH.ProjectServices.API.Infrastructure.Persistence.Configurations
{
    public class PGProjectConfigurations : IEntityTypeConfiguration<PGProject>
    {
        public void Configure(EntityTypeBuilder<PGProject> builder)
        {
            builder.ToTable(Constants.ADM_PG_PROJECT, Constants.DWH_META);
            builder.Property(p => p.Id).HasColumnName("PRODUCTGROUP_ID");
            builder.Property(p => p.ProjectId).HasColumnName("PROJECT_ID");

        }
    }
}
