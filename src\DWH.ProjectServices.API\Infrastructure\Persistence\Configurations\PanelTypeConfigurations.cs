﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Models;

namespace DWH.ProjectServices.API.Infrastructure.Persistence.Configurations
{
    public class PanelTypeConfigurations : IEntityTypeConfiguration<BaseProjectPanelType>
    {
        public void Configure(EntityTypeBuilder<BaseProjectPanelType> builder)
        {
            builder.ToTable(Constants.ADM_PRJ_PANELTYPE, Constants.DWH_META);
            builder.Property(p => p.Id).HasColumnName("PANELTYPE_ID");
            builder.Property(p => p.Name).HasColumnName("PANELTYPE_DESC");

        }
    }
}
