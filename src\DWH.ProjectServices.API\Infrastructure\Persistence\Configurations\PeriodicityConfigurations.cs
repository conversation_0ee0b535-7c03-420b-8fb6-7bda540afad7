﻿using DWH.ProjectServices.API.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace DWH.ProjectServices.API.Infrastructure.Persistence.Configurations
{
    public class PeriodicityConfigurations : IEntityTypeConfiguration<Periodicity>
    {
        public void Configure(EntityTypeBuilder<Periodicity> builder)
        {
            builder.ToTable(Constants.ADM_PE_PERIODICITY, Constants.DWH_META);
            builder.Property(p => p.PERIODICITY_ID).HasColumnName("PERIODICITY_ID");
            builder.Property(p => p.PERIODICITY_DDESC).HasColumnName("PERIODICITY_DDESC");
        }
    }
}
