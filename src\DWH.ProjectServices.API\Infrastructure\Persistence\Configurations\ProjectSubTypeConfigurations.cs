﻿using DWH.ProjectServices.API.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace DWH.ProjectServices.API.Infrastructure.Persistence.Configurations
{
    public class ProjectSubTypeConfigurations : IEntityTypeConfiguration<ProjectSubType>
    {
        public void Configure(EntityTypeBuilder<ProjectSubType> builder)
        {
            builder.ToTable(Constants.ZZZ_SUBPROJECTTYPE, Constants.DWH_META);
            builder.Property(p => p.Id).HasColumnName("SUBPROJECTTYPEID");
            builder.Property(p => p.Name).HasColumnName("SUBPROJECTTYPENAME");
        }
    }
}
