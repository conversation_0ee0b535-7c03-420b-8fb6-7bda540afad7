﻿using DWH.ProjectServices.API.Models;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;

namespace DWH.ProjectServices.API.Infrastructure.Persistence.Configurations
{
    public class ReportingBaseProjectConfigurations : IEntityTypeConfiguration<ReportingBaseProject>
    {
        public void Configure(EntityTypeBuilder<ReportingBaseProject> builder)
        {
            builder.ToTable(Constants.ADM_PRJ_BASEREPPROJECT, Constants.DWH_META);
            builder.Property(p => p.Id).HasColumnName("REPPROJECTID");
            builder.Property(p => p.BaseProjectId).HasColumnName("BASEPROJECTID");
            builder.Property(p => p.Deleted).HasColumnName("DELETED");
            builder.Property(p => p.CreatedBy).HasColumnName("CREATEDBY");
            builder.Property(p => p.<PERSON>When).HasColumnName("CREATEDWHEN");
            builder.Property(p => p.ChangedBy).HasColumnName("CHANGEDBY");
            builder.Property(p => p.ChangedWhen).HasColumnName("CHANGEDWHEN");
        }
    }
}
