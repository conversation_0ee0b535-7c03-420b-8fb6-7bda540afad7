﻿using AutoMapper;
using DWH.ProjectServices.API.Models.Dtos;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;

namespace DWH.ProjectServices.API.Infrastructure.Persistence.Entities
{

    [Table("BaseProject_Predecessor")]
    public class BaseProjectPredecessorEntity
    {
        [Key]
        public int Id { get; set; }

        public int BaseProjectId { get; set; }
        public int PredecessorId { get; set; }

    }
}
