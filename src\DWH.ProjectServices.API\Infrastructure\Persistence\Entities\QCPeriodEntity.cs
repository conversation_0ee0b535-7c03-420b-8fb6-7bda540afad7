﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DWH.ProjectServices.API.Infrastructure.Persistence.Entities
{
    [Table("QCPeriod")]
    public class QCPeriodEntity
    {
        [Key]
        public long Id { get; set; }
        public int QCProjectId { get; set; }
        public long PeriodId { get; set; }
        public string CreatedBy { get; set; }
        public DateTimeOffset CreatedWhen { get; set; }
        public string UpdatedBy { get; set; }
        public DateTimeOffset? UpdatedWhen { get; set; }
        public ICollection<PeriodEntity> Periods { get; set; }
        public StockInitializationEntity StockInitialization { get; set; }
    }


}
