﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DWH.ProjectServices.API.Infrastructure.Persistence.Entities
{
    [Table("RetailerSeperation")]
    public class RetailerSeperationEntity
    {
        [Key]
        public int Id { get; set; }

        public int SourceBPId { get; set; }

        public int RetailerBPId { get; set; }

        public int RetailerSeperationRequestId { get; set; }
        
        public bool IsError { get; set; }
    }

}
