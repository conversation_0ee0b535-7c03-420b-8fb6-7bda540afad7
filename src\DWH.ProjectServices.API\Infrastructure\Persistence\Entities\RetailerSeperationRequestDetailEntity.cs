﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DWH.ProjectServices.API.Infrastructure.Persistence.Entities
{
    [Table("RetailerSeperationRequestDetail")]
    public class RetailerSeperationRequestDetailEntity
    {
        [Key]
        public int Id { get; set; }

        public int RetailerSeperationRequestId { get; set; }

        public string UpdatedBy { get; set; }

        public DateTimeOffset UpdatedWhen { get; set; }

        public int RequestStatusId { get; set; }
    }
}
