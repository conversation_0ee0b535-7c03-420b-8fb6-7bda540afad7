﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DWH.ProjectServices.API.Infrastructure.Persistence.Entities
{
    [Table("RetailerSeperationRequest")]
    public class RetailerSeperationRequestEntity
    {
        [Key]
        public int Id { get; set; }

        public long FromPeriodId { get; set; }

        public long ToPeriodId { get; set; }

        public bool ResetCorrection { get; set; }

        public bool Extrapolation { get; set; }

        public string JiraId { get; set; }

        public int RequestStatusId { get; set; }

        public ICollection<RetailerSeperationEntity> RetailerSeperations { get; set; }

        public ICollection<RetailerSeperationRequestDetailEntity> RetailerSeperationRequestDetails { get; set; }
    }

}
