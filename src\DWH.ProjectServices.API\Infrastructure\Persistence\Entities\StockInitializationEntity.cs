﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DWH.ProjectServices.API.Infrastructure.Persistence.Entities
{
    [Table("StockInitialization")]
    public class StockInitializationEntity
    {
        [Key]
        public int Id { get; set; }
        public int? StockBaseProjectId { get; set; }
        public long? StockPeriodId { get; set; }
        public long QCPeriodId { get; set; }
    }
}
