﻿using DWH.ProjectServices.API.Infrastructure.Persistence.Entities;
using DWH.ProjectServices.API.Models;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

namespace DWH.ProjectServices.API.Infrastructure.Persistence
{
    public class PostgreSqlDbContext : DbContext
    {
        public PostgreSqlDbContext(DbContextOptions<PostgreSqlDbContext> options) : base(options)
        {
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<BaseProjectProductGroupEntity>().HasQueryFilter(b => !b.Deleted.HasValue || !b.Deleted.Value);
            //modelBuilder.Entity<BaseProjectEntity>().HasQueryFilter(b => !b.Deleted.Value);
            modelBuilder.Entity<BaseProjectEntity>().Property(b => b.<PERSON>).HasDefaultValue(DateTimeOffset.UtcNow);
            modelBuilder.Entity<QCProjectEntity>().HasIndex(qc => qc.ResetCorrectionTypeId).IsUnique(false);
            modelBuilder.Entity<QCPeriodEntity>().Property(b => b.CreatedWhen).HasDefaultValue(DateTimeOffset.UtcNow);

            modelBuilder.Entity<BaseProjectEntity>()
                .HasOne(bp => bp.QCProjects)
                .WithOne() 
                .HasForeignKey<QCProjectEntity>(qc => qc.BaseProjectId)
                .HasConstraintName("FK_BaseProject_QCProjects_BaseProjectId");

            modelBuilder.Entity<BaseProjectEntity>()
                .HasMany(bp => bp.ProductGroups)
                .WithOne()
                .HasForeignKey(pg => pg.BaseProjectId)
                .HasConstraintName("FK_BaseProject_ProductGroups_BaseProject_BaseProjectId");

            modelBuilder.Entity<BaseProjectEntity>()
                .HasMany(bp => bp.Predecessors)
                .WithOne()
                .HasForeignKey(p => p.BaseProjectId)
                .HasConstraintName("FK_BaseProject_Predecessors_BaseProject_BaseProjectId");

            modelBuilder.Entity<QCProjectEntity>()
                .HasMany(bp => bp.QCPeriods) 
                .WithOne() 
                .HasForeignKey(p => p.QCProjectId) 
                .HasConstraintName("FK_QCPeriod_QCProject_QCProjectId");

            modelBuilder.Entity<QCPeriodEntity>()
                .HasMany(qp => qp.Periods)
                .WithOne()
                .HasForeignKey(p => p.QCPeriodId)
                .HasConstraintName("FK_Period_QCPeriod_QCPeriodId");

            modelBuilder.Entity<QCPeriodEntity>()
                .HasOne(bp => bp.StockInitialization)
                .WithOne()
                .HasForeignKey<StockInitializationEntity>(s => s.QCPeriodId)
                .HasConstraintName("FK_StockInitialization_QCPeriod_QCPeriodId");

            modelBuilder.Entity<RetailerSeperationRequestEntity>()
                .HasMany(rsr => rsr.RetailerSeperations)
                .WithOne()
                .HasForeignKey(rs => rs.RetailerSeperationRequestId)
                .HasConstraintName("FK_RetailerSeperation_RetailerSeperationRequests_RetailerSeper~");

            modelBuilder.Entity<RetailerSeperationRequestEntity>()
                .HasMany(rsr => rsr.RetailerSeperationRequestDetails)
                .WithOne()
                .HasForeignKey(rsd => rsd.RetailerSeperationRequestId)
                .HasConstraintName("FK_RetailerSeperationRequestDetail_RetailerSeperationRequests_~");

        }
        public DbSet<BaseProjectEntity> BaseProjects { get; set; }

        public DbSet<BaseProjectPanelEntity> BaseProjectPanels { get; set; }

        public DbSet<BaseProjectDataType> BaseProjectDataTypes { get; set; }

        public DbSet<BaseProjectPurpose> BaseProjectPurposes { get; set; }

        public DbSet<QCProjectEntity> QCProjects { get; set; }

        public DbSet<ResetCorrectionType> ResetCorrectionTypes { get; set; }

        public DbSet<QCPeriodEntity> QCPeriod { get; set; }
        public DbSet<PeriodEntity> Period { get; set; }

        public DbSet<RetailerSeperationRequestEntity> RetailerSeperationRequests { get; set; }
        public DbSet<RetailerSeperationRequestDetailEntity> RetailerSeperationRequestDetail { get; set; }

        public DbSet<RequestStatusEntity> RequestStatus { get; set; }
        public DbSet<RetailerSeperationEntity> RetailerSeperation { get; set; }
        public DbSet<OutBoxItemEntity> OutBoxItem { get; set; }

    }
}
