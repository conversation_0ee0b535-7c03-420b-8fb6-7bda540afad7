﻿using AutoMapper;
using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;
using DWH.ProjectServices.API.Infrastructure.Persistence.Entities;
using DWH.ProjectServices.API.Models;
namespace DWH.ProjectServices.API.Infrastructure.Persistence.Profile
{
    public class BaseProjectsProfile : AutoMapper.Profile
    {
        public BaseProjectsProfile()
        {
            CreateMap<BaseProjectResponse, BaseProject>()
           .ForMember(dest => dest.CreatedWhen, opt => opt.MapFrom(src => src.DateOfCreation))
           .ReverseMap();
            CreateMap<BaseProjectListPayloadResponse, BaseProject>()
            .ReverseMap()
            .AfterMap((src, dest) =>
            {
                if (src.UpdatedWhen == null)
                {
                    dest.UpdatedWhen = src.CreatedWhen;
                }

                if (string.IsNullOrEmpty(src.UpdatedBy))
                {
                    dest.UpdatedBy = src.CreatedBy;
                }
            });
            CreateMap<BaseProjectEntity, BaseProject>().ReverseMap();

            CreateMap<BaseProjectPredecessorEntity, BaseProjectPredecessor>().ReverseMap();

            CreateMap<DataTypes, BaseProjectDataType>()
           .ForMember(dest => dest.Desc, opt => opt.MapFrom(src => src.Description))
           .ReverseMap();

            CreateMap<Purposes, BaseProjectPurpose>().ReverseMap();
            CreateMap<BaseProjectPanel, BaseProjectPanelEntity>().ReverseMap();
            CreateMap<ResetCorrectionType, ResetCorrectionTypes>().ReverseMap();

            CreateMap<ResetCorrectionTypeResponse, ResetCorrectionTypes>().ReverseMap();
            CreateMap<ProjectSubTypeResponse, ProjectSubTypes>().ReverseMap();
            CreateMap<ProjectSubType, ProjectSubTypes>().ReverseMap();
            CreateMap<DependencyResponse, Dependencies>().ReverseMap();
            CreateMap<BaseProjectDeleteRequest, BaseProjectDeletes>().ReverseMap();
            CreateMap<BaseProjectsLists, BaseProject>().ReverseMap();
            CreateMap<ProjectsDependencies, ProjectDependencies>().ReverseMap();

            CreateMap<BaseProjectNameandIdResponse, BaseProjectNameandIds>().ReverseMap();
            CreateMap<BaseProjectEntity, BaseProjectNameandIds>().ReverseMap();
            CreateMap<BaseProjectNameandIdLists, BaseProjectNameandIds>().ReverseMap();
            CreateMap<BaseProjectCountryRequest, BaseProjectCountries>().ReverseMap();
        }
    }
}
