﻿using AutoMapper;
using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;
using DWH.ProjectServices.API.Infrastructure.Persistence.Entities;
namespace DWH.ProjectServices.API.Infrastructure.Persistence.Profile
{
    public class QCProjectProfile : AutoMapper.Profile
    {
        public QCProjectProfile()
        {
            CreateMap<QCPeriod, QCPeriodEntity>()
           .ReverseMap();
            CreateMap<Period, PeriodEntity>().ReverseMap();
            CreateMap<StockInitialization, StockInitializationEntity>().ReverseMap();
            CreateMap<BaseProjectProductGroupEntity, BaseProjectProductGroup>().ReverseMap();
            CreateMap<QCProject, QCProjectEntity>().ReverseMap();
            CreateMap<QCProject, QCPeriodEntity>().ReverseMap();
            CreateMap<QCProjectEntity, QCProjectUpdates>().ReverseMap();
            CreateMap<QCPeriodEntity, QCPeriodModelDto>().ReverseMap();
            CreateMap<QCPeriodEntity, QCPeriodWithBPIdResponse>().ReverseMap();

        }
    }
}
