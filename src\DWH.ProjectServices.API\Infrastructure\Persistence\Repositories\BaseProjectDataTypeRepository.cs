﻿using AutoMapper;
using BootstrapAPI.Core.Exception.Instances;
using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Infrastructure.Persistence.Entities;
using DWH.ProjectServices.API.Infrastructure.Persistence.Repositories.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace DWH.ProjectServices.API.Infrastructure.Persistence.Repositories
{
    public class BaseProjectDataTypeRepository : IBaseProjectDataTypeRepository
    {
        private readonly PostgreSqlDbContext _postdbContext;
        private readonly IMapper _mapper;
        public BaseProjectDataTypeRepository(PostgreSqlDbContext postdbContext, IMapper mapper)
        {
            _postdbContext = postdbContext;
            _mapper = mapper;
        }

        public async Task<IReadOnlyCollection<DataTypes>> GetAllAsync()
        {
            var baseProjectDataTypeEntities = await _postdbContext.BaseProjectDataTypes.ToListAsync();
            if (!baseProjectDataTypeEntities.Any())
                throw new EntityNotExistsException($"BaseProject Data Types Not Found ");

             return _mapper.Map<IReadOnlyCollection<DataTypes>>(baseProjectDataTypeEntities);
        }
    }
}
