﻿using AutoMapper;
using BootstrapAPI.Core.Exception.Instances;
using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Infrastructure.Persistence.Entities;
using DWH.ProjectServices.API.Infrastructure.Persistence.Repositories.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace DWH.ProjectServices.API.Infrastructure.Persistence.Repositories
{
    public class BaseProjectPurposeRepository : IBaseProjectPurposeRepository
    {
        private readonly PostgreSqlDbContext _postdbContext;
        private readonly IMapper _mapper;

        public BaseProjectPurposeRepository(PostgreSqlDbContext postdbContext, IMapper mapper)
        {
            _postdbContext = postdbContext;
            _mapper = mapper;
    }

        public async Task<IReadOnlyCollection<Purposes>> GetAllAsync()
        {
            var baseProjectPurposes = await _postdbContext.BaseProjectPurposes.OrderBy(p => p.Name).ToListAsync();
            if (!baseProjectPurposes.Any())
                throw new EntityNotExistsException($"BaseProject Panels Not Found ");

            return _mapper.Map<IReadOnlyCollection<Purposes>>(baseProjectPurposes); ;
        }
    }
}
