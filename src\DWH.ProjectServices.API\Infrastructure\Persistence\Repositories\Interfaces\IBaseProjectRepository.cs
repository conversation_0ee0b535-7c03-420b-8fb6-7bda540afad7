﻿using AutoMapper;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;
using DWH.ProjectServices.API.Domain.Models;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;
using static DWH.ProjectServices.API.Infrastructure.Persistence.Repositories.BaseProjectRepository;
using DWH.ProjectServices.API.Infrastructure.Persistence.Entities;


namespace DWH.ProjectServices.API.Infrastructure.Persistence.Repositories.Interfaces
{
    public interface IBaseProjectRepository
    {

        Task<BaseProject> AddAsync(BaseProject newBaseProject);
        Task<IEnumerable<BaseProject>> GetAllAsync(BaseProjectPredecessor baseProjectPredecessorRequest);
        Task<BaseProject> UpdateAsync(int baseProjectId, BaseProject baseProjectEditRequest,bool isPGDeleted);
        Task<BaseProject> GetAsync(int baseProjectId);
        Task<IEnumerable<BaseProject>> GetAsyncList(BaseProjectsLists baseProjectListsRequest);

        Task<IReadOnlyList<Dependencies>> DeleteAsync(List<int> baseProjectIds, string deletedBy);

        Task<ProjectsDependencies> CheckDependencies(int baseProjectId);

        Task<BaseProject> GetBaseProjectAsync(int qcProjectId);

        Task<BaseProjectEntity> UpdateTypeIdAsync(int baseProjectId, int TypeId, string username, PostgreSqlDbContext dbContext);

        BaseProjectEntity CreateRetailerBaseProject(BaseProjectEntity existingBaseProject,string username);
        Task<IReadOnlyList<BaseProjectNameandIds>> GetAsyncListBaseProjects(BaseProjectNameandIdLists baseProjectListsRequest);
        Task<List<int>> GetBaseProjectWithQCStatus(QCStatuses qcStatuses);

        Task<List<long>> GetQCPeriodWithQCStatus(QCStatuses qcStatuses);
        Task<bool> CheckRetailerSeparationState(int baseProjectsId);

        Task<List<int>> GetByBaseProjectIdAsync(BaseProjectCountries baseProjectCountryRequest);
        Task<int[]> CheckDataLoading(List<int> productGroups, int baseProjectId);
        Task<List<RBPGDependencies>> CheckProductGroupRBBPDependency(List<int> productGroups, int baseProjectId);
        Task<int> GetPeriodicityIdAsync(int baseProjectId);

        Task<IEnumerable<QCPeriod>> GetQCPeriodAsync(int baseProjectId);
        Task<BaseProjectDependencies> CheckBaseProjectDependencies(int baseProjectId);
        Task<IReadOnlyList<string>> GetUsersList();
        Task<IReadOnlyList<Dependencies>> AddBulkAsync(List<int> baseProjectIds, string userName, Dictionary<int, QCProject> qcProjectsMap = null, List<BaseProjectEntity> sourceBaseProjects = null);
        Task<List<BaseProjectEntity>> GetSourceBaseProjectsWithRelatedDataAsync(List<int> baseProjectIds);
    }
}
