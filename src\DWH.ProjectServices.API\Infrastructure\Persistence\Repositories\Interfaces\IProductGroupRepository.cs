﻿using DWH.ProjectServices.API.Models;
using DWH.ProjectServices.API.Models.Dtos;

namespace DWH.ProjectServices.API.Infrastructure.Persistence.Repositories.Interfaces
{
    public interface IProductGroupRepository
    {
        Task<IEnumerable<ProductGroup>> GetAllAsync(ProductGroupRequest productGroupRequestDto);
        string GetPeriodicityDesc(int periodicityId);
        Task<IEnumerable<DomainProductGroup>> GetDomainProductGroupAsync(DescriptionRequest descriptionDto);
    }
}
