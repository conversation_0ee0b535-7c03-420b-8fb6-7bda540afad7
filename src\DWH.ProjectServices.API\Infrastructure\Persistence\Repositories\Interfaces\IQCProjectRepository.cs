﻿using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;

namespace DWH.ProjectServices.API.Infrastructure.Persistence.Repositories.Interfaces
{
    public interface IQCProjectRepository
    {
        Task<QCProject> UpdateAsync(int qcProjectId, QCProjectUpdates qcProjectEditRequest);
        Task<List<int>> GetByQCProjectIdAsync(QCProjectCountries qcProjectCountryRequest);
        Task <int> GetFilteredQCProjectIdAsync(QCProjectCountryIds qcProjectCountryId);
    }
}
