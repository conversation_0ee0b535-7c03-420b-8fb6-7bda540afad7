﻿using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Infrastructure.Persistence.Entities;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;

namespace DWH.ProjectServices.API.Infrastructure.Persistence.Repositories.Interfaces
{
    public interface IRetailerSeperationRepository
    {
        Task<RetailerSeperationRequest> AddAsync(RetailerSeperationRequest RetailerSeperation);
        Task<RetailerSeperationRequest> GetAsync(int requestDetailId);
        Task<IReadOnlyList<int>> GetAuthorizedBaseProjectsByCountry(RetailerSeparationCountries rsCountryRequest);
        Task<RetailerSeperationRequest> UpdateAsync(string username, RetailerSeperationRequest retailerSeperation);
        Task<IReadOnlyList<RetailerSeperationRequest>> GetAsyncList(RetailerSeperationsLists retailerSeperations);
        Task<IReadOnlyList<string>> GetUsersList();
        Task<List<int>> GetBaseProjectsbyRequestIdAsync(int retailerSeperationRequestId);
        Task AddRetailerBPIdAsync(int sourceBPId, int retailerSeperationRequestId, int newRetailerBPId);
        Task AddDetailAndUpdateStatusAsync(RetailerSeperationRequestDetail details);
        Task<bool> UpdateStatusAsync(RetailerSeparationStatusDetails rsStatusDetails);

        Task<IReadOnlyList<ResponseInfoRetailerSeperation>> DeleteSourceBPAsync(IRSeperationDeletes IRSeperationDeleteRequest);
        Task<IReadOnlyList<int>> GetAuthorizedRetailerSeperationsByCountry(RetailerSeparationCountries rsCountryRequest);

        Task<BaseProject> PerformRetailerSeparation(int sourceBPId,int TypeId,string username, int retailerSeperationRequestId);
        Task<int> GetQCProjectIdAsync(int sourceBPId);
        Task<List<int>> GetRetailerSeperationRequestIdsBySourceBpIdAsync(int sourceBpId);
        Task<string> GetJiraId(int requestDetailId);
        Task UpdateRetailerSeparationRequestStatus(int retailerRequestId);

    }
}
