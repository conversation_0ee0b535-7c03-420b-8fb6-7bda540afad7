﻿using DWH.ProjectServices.API.Models;
using Microsoft.EntityFrameworkCore;
using System.Runtime.ExceptionServices;
using AutoMapper;
using BootstrapAPI.Core.Exception.Instances;
using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Infrastructure.Persistence;
using DWH.ProjectServices.API.Infrastructure.Persistence.Repositories.Interfaces;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;

namespace DWH.ProjectServices.API.Infrastructure.Persistence.Repositories
{
    public class ProjectSubTypeRepository : IProjectSubTypeRepository
    {
        private readonly OracleDbContext _dbContext;
        private readonly IMapper _mapper;

        public ProjectSubTypeRepository(OracleDbContext dbContext, IMapper mapper)
        {
            _dbContext = dbContext;
            _mapper = mapper;
        }

        public async Task<IReadOnlyCollection<ProjectSubTypes>> FindAllAsync()
        {
            var projectSubTypeEntities = await _dbContext.ProjectSubTypes.ToListAsync();
            if (!projectSubTypeEntities.Any())
                throw new EntityNotExistsException($"ProjectSubType Not Found ");
            return projectSubTypeEntities?.Select(p => new ProjectSubTypes(p.Id, p.Name)).ToList();
        }
    }
}


