﻿using AutoMapper;
using BootstrapAPI.Core.Exception.Instances;
using DWH.ProjectServices.API.Domain.Models;
using Microsoft.EntityFrameworkCore;
using System.Net;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;
using DWH.ProjectServices.API.Infrastructure.Persistence.Entities;
using DWH.ProjectServices.API.Infrastructure.Persistence.Repositories.Interfaces;
using DWH.ProjectServices.API.Services.Helper.Interface;

namespace DWH.ProjectServices.API.Infrastructure.Persistence.Repositories
{
    public class QCPeriodRepository : IQCPeriodRepository
    {
        private readonly PostgreSqlDbContext _postdbContext;
        private readonly IMapper _mapper;

        public QCPeriodRepository(PostgreSqlDbContext postdbContext, IMapper mapper)
        {
            _postdbContext = postdbContext;
            _mapper = mapper;
        }

        public async Task<QCPeriod> AddAsyncQCPeriod(QCPeriod qcPeriodCreateRequest)
        {
            var qcPeriod = _mapper.Map<QCPeriodEntity>(qcPeriodCreateRequest);
            qcPeriod.CreatedWhen = DateTime.UtcNow;
            _postdbContext.QCPeriod.Add(qcPeriod);
            await _postdbContext.SaveChangesAsync();
            var qcPeriods = _mapper.Map<QCPeriod>(qcPeriod);
            return qcPeriods;

        }

        public async Task<QCPeriod> EditPeriodAsync(long qcPeriodId, QCPeriodEdits qcPeriodEditRequest)
        {
            var qcPeriod = await _postdbContext.QCPeriod
                .Include(qcp => qcp.Periods)
                .Include(qcp => qcp.StockInitialization)
                .FirstOrDefaultAsync(qcp => qcp.Id == qcPeriodId);

            if (qcPeriod != null)
            {
                foreach (var periodEditRequest in qcPeriodEditRequest.Periods)
                {
                    var periodToUpdate = qcPeriod.Periods.SingleOrDefault(p => p.index == periodEditRequest.index);

                    if (periodToUpdate != null)
                    {
                        periodToUpdate.RefProjectId = periodEditRequest.RefProjectId;
                        periodToUpdate.RefPeriodId = periodEditRequest.RefPeriodId;
                    }
                    else
                    {
                        CreateNewPeriod(qcPeriod, periodEditRequest);
                    }

                }

                if (qcPeriodEditRequest.StockInitialization != null)
                {
                    if (qcPeriod.StockInitialization == null)
                    {
                        qcPeriod.StockInitialization = new StockInitializationEntity();
                        qcPeriod.StockInitialization.QCPeriodId = qcPeriodId;
                    }
                    qcPeriod.StockInitialization.StockBaseProjectId = qcPeriodEditRequest.StockInitialization.StockBaseProjectId;
                    qcPeriod.StockInitialization.StockPeriodId = qcPeriodEditRequest.StockInitialization.StockPeriodId;
                }

                qcPeriod.UpdatedWhen = DateTime.UtcNow;
                qcPeriod.UpdatedBy = qcPeriodEditRequest.UpdatedBy;
                await _postdbContext.SaveChangesAsync();
                return _mapper.Map<QCPeriod>(qcPeriod);
            }
            else
            {
                throw new EntityNotExistsException($"No Period exists with Id {qcPeriod}");
            }

        }

        private void CreateNewPeriod(QCPeriodEntity qcPeriod, RefPeriodsEditRequest periodEditRequest)
        {
            var newPeriod = new PeriodEntity
            {
                index = periodEditRequest.index.Value,
                RefProjectId = periodEditRequest.RefProjectId,
                RefPeriodId = periodEditRequest.RefPeriodId,
            };

            qcPeriod.Periods.Add(newPeriod);
        }

        public async Task<IEnumerable<QCPeriod>> GetAllQCPeriodsAsync(int qcProjectId)
        {
            var qcPeriods = await _postdbContext.QCProjects
                                    .Where(qcp => qcp.Id == qcProjectId)
                                    .SelectMany(qcp => qcp.QCPeriods).Include(qcpd => qcpd.Periods.OrderBy(period => period.index))
                                    .Include(si => si.StockInitialization)
                                    .OrderByDescending(qcp => qcp.PeriodId)
                                    .ToListAsync();

            if (!qcPeriods.Any())
            {
                throw new EntityNotExistsException($"No QCPeriods exists with Id {qcProjectId}");
            }
            return _mapper.Map<IEnumerable<QCPeriod>>(qcPeriods);

        }

        public async Task<QCPeriodWithBPIdResponse> GetQCPeriodAsync(long qcPeriodId)
        {

            var qcProjects = await _postdbContext.QCProjects.Include(qcp => qcp.QCPeriods).ThenInclude(qcpd => qcpd.Periods).Include(qcp => qcp.QCPeriods).ThenInclude(qcpd => qcpd.StockInitialization).Where(qcp => qcp.QCPeriods.Any(qcpd => qcpd.Id == qcPeriodId)).ToListAsync();
            var qcPeriod = qcProjects.SelectMany(qcp => qcp.QCPeriods.Where(qcpd => qcpd.Id == qcPeriodId).Select(qcpd => new QCPeriodWithBPIdResponse
            {
                Id = qcpd.Id,
                BaseProjectId = qcp.BaseProjectId,
                QCProjectId = qcp.Id,
                PeriodId = qcpd.PeriodId,
                Periods = _mapper.Map<ICollection<Period>>(qcpd.Periods),
                StockInitialization = _mapper.Map<StockInitialization>(qcpd.StockInitialization),
                DateOfCreation = qcpd.CreatedWhen,
                UpdatedWhen = qcpd.UpdatedWhen,
                CreatedBy = qcpd.CreatedBy,
                UpdatedBy = qcpd.UpdatedBy
            }))
            .FirstOrDefault();

            var qcPeriodResponseDto = _mapper.Map<QCPeriodWithBPIdResponse>(qcPeriod);

            if (qcPeriod is null)
            {
                throw new EntityNotExistsException($"No QCPeriod exists with Id {qcPeriodId}");
            }
            return qcPeriodResponseDto;

        }

        public async Task<IReadOnlyList<ResponseInfoQCPeriod>> DeletePeriodAsync(QCPeriodDeletes qcPeriodDeleteRequest)
        {

            var responses = new List<ResponseInfoQCPeriod>();
            foreach (var qcPeriodId in qcPeriodDeleteRequest.Ids)
            {
                var qcPeriod = await _postdbContext.QCPeriod
                    .Include(qcp => qcp.Periods)
                    .FirstOrDefaultAsync(qcp => qcp.Id == qcPeriodId);

                if (qcPeriod != null)
                {
                    _postdbContext.QCPeriod.Remove(qcPeriod);
                    responses.Add(new ResponseInfoQCPeriod(qcPeriodId.ToString(), (int)HttpStatusCode.OK, HttpStatusCode.OK.ToString()));
                }
                else
                {
                    responses.Add(new ResponseInfoQCPeriod(qcPeriodId.ToString(), (int)HttpStatusCode.NotFound, HttpStatusCode.NotFound.ToString()));
                }
            }

            try
            {
                await _postdbContext.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                responses.Add(new ResponseInfoQCPeriod("Error during deletion", (int)HttpStatusCode.InternalServerError, HttpStatusCode.InternalServerError.ToString()));
            }
            return responses;
        }


        public async Task<bool> CheckIfQCPeriodExists(int qcProjectId, long periodId)
        {
            var qcPeriods = await _postdbContext.QCProjects
                .Where(qcp => qcp.Id == qcProjectId)
                .SelectMany(qcp => qcp.QCPeriods)
                .Where(qcpd => qcpd.PeriodId == periodId)
                .ToListAsync();

            return qcPeriods.Any();
        }

        public async Task<List<long>> GetAuthorizedCountriesForPeriods(QCPeriodCountries qcPeriodCountryRequest)
        {
            var baseProject = await _postdbContext.BaseProjects
                .Include(b => b.QCProjects)
                .Where(b => qcPeriodCountryRequest.CountryIds.Contains(b.CountryId)
                    && b.QCProjects.QCPeriods.Any(p => qcPeriodCountryRequest.QCPeriodIds.Contains(p.Id))
                    && b.Deleted == false)
                .SelectMany(b => b.QCProjects.QCPeriods
                .Where(p => qcPeriodCountryRequest.QCPeriodIds.Contains(p.Id))
                .Select(p => p.Id))
                .Distinct()
                 .ToListAsync();
            return baseProject;
        }
    }
}
