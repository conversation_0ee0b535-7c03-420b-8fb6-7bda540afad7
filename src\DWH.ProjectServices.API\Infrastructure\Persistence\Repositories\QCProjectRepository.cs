﻿using AutoMapper;
using BootstrapAPI.Core.Exception.Instances;
using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Infrastructure.Persistence;
using DWH.ProjectServices.API.Infrastructure.Persistence.Repositories.Interfaces;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;
using DWH.ProjectServices.API.Services.Helper.Interface;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace DWH.ProjectServices.API.Infrastructure.Persistence.Repositories
{
    public class QCProjectRepository : IQCProjectRepository
    {
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private readonly PostgreSqlDbContext _postdbContext;
        private readonly IMapper _mapper;

        public QCProjectRepository(PostgreSqlDbContext postdbContext, IMapper mapper, IServiceScopeFactory serviceScopeFactory)
        {
            _postdbContext = postdbContext;
            _mapper = mapper;
            _serviceScopeFactory = serviceScopeFactory;
        }

        public async Task<QCProject> UpdateAsync(int qcProjectId, QCProjectUpdates qcProjectReq)
        {
            using var scope = _serviceScopeFactory.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<PostgreSqlDbContext>();

            var qcProject = await dbContext
                                    .QCProjects
                                    .SingleOrDefaultAsync(b => b.Id == qcProjectId);
            if (qcProject != null)
            {
                qcProject.IsAutomatedPriceCheck = qcProjectReq.IsAutomatedPriceCheck;
                qcProject.SQCMode = qcProjectReq.SQCMode;
                qcProject.IsAutoLoad = qcProjectReq.IsAutoLoad;
                qcProject.ResetCorrectionTypeId = qcProjectReq.ResetCorrectionTypeId;


                //update time and updatedby for baseporject
                var baseProject = await dbContext
                                  .BaseProjects
                                  .SingleOrDefaultAsync(b => b.Id == qcProject.BaseProjectId);

                baseProject.UpdatedWhen = DateTime.UtcNow;
                baseProject.UpdatedBy = qcProjectReq.UpdatedBy;

                await dbContext.SaveChangesAsync();
                return _mapper.Map<QCProject>(qcProject);
            }
            else
            {
                throw new EntityNotExistsException($"No QC Project exists with Id {qcProjectId}",
                                                    "QC Project", qcProjectId);
            }
        }

        public async Task<List<int>> GetByQCProjectIdAsync(QCProjectCountries qcProjectCountryRequest)
        {
            var qcProjects = await _postdbContext.BaseProjects
                .Include(b => b.QCProjects)
                .Where(b => qcProjectCountryRequest.QCProjectIds.Contains(b.QCProjects.Id)
                    && qcProjectCountryRequest.CountryIds.Contains(b.CountryId)
                    && b.Deleted == false)
                .Select(b => b.QCProjects.Id)
                .Distinct()
                .ToListAsync();
            return qcProjects;
        }       

        public async Task<int> GetFilteredQCProjectIdAsync(QCProjectCountryIds qcProjectCountryId)
        {
            var qcProject = await _postdbContext.BaseProjects
               .Include(b => b.QCProjects)
               .Where(b => qcProjectCountryId.QCProjectId==b.QCProjects.Id
                   && qcProjectCountryId.CountryIds.Contains(b.CountryId)
                   && b.Deleted == false)
               .Select(b => b.QCProjects.Id).FirstOrDefaultAsync();

            return qcProject;
        }


    }
}