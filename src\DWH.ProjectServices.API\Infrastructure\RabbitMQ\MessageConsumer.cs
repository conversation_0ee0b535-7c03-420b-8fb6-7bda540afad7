﻿using DWH.ProjectServices.API.Infrastructure.RabbitMQ.Constants;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ.Interfaces;
using RabbitMQ.Client.Events;
using RabbitMQ.Client;
using DWH.ProjectServices.API.Services.Interfaces;

namespace DWH.ProjectServices.API.Infrastructure.RabbitMQ
{
    public class MessageConsumer : IMessageConsumer
    {
        private readonly ILogger<MessageConsumer> _logger;
        public IChannel _channel;
        public IConnection _connection;
        IRabbitMQConnectionFactory _connectionFactory;
        IMessageSync _msgSync;
        IErrorHandler _errorHandler;

        public MessageConsumer(IRabbitMQConnectionFactory connectionFactory, IMessageSync msgSync, ILogger<MessageConsumer> logger, IErrorHandler errorHandler)
        {
            _logger = logger;
            _connectionFactory = connectionFactory;
            _connection = connectionFactory.CreateConnectionAsync().GetAwaiter().GetResult();
            _channel = _connection.CreateChannelAsync().GetAwaiter().GetResult();
            _msgSync = msgSync;
            _errorHandler = errorHandler;
        }

        public async Task ConsumeAndSync()
        {
            var queueName = RMQConstants.RetailerSeparationQueue;
            var consumer = new AsyncEventingBasicConsumer(_channel);

            consumer.ReceivedAsync += async (model, ea) =>
            {
                try
                {
                    await _msgSync.PerformSync(ea);
                    await _channel.BasicAckAsync(ea.DeliveryTag, false);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing message");
                    _errorHandler.HandleErrorMessages(_channel, ea);
                }
            };

            await _channel.BasicConsumeAsync(queueName, false, consumer);

        }
    }
}
