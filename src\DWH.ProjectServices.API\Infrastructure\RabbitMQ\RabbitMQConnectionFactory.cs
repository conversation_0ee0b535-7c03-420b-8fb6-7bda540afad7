﻿using System;
using System.Threading.Tasks;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ.Configuration;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ.Interfaces;
using Microsoft.Extensions.Options;
using RabbitMQ.Client;

namespace DWH.ProjectServices.API.Infrastructure.RabbitMQ
{
    public class RabbitMQConnectionFactory : IRabbitMQConnectionFactory
    {
        private readonly ConnectionFactory _factory;
        private readonly RabbitMQSettings _rabbitMQSettings;
        private IConnection _connection; // Singleton Connection

        public RabbitMQConnectionFactory(IOptions<RabbitMQSettings> rabbitMQSettings)
        {
            _rabbitMQSettings = rabbitMQSettings?.Value ?? throw new ArgumentNullException(nameof(rabbitMQSettings), "RabbitMQ settings must not be null");

            ArgumentException.ThrowIfNullOrEmpty(_rabbitMQSettings.HostName, nameof(_rabbitMQSettings.HostName));
            ArgumentException.ThrowIfNullOrEmpty(_rabbitMQSettings.UserName, nameof(_rabbitMQSettings.UserName));
            ArgumentException.ThrowIfNullOrEmpty(_rabbitMQSettings.Password, nameof(_rabbitMQSettings.Password));
            ArgumentException.ThrowIfNullOrEmpty(_rabbitMQSettings.VirtualHost, nameof(_rabbitMQSettings.VirtualHost));

            _factory = new ConnectionFactory
            {
                HostName = _rabbitMQSettings.HostName,
                UserName = _rabbitMQSettings.UserName,
                Password = _rabbitMQSettings.Password,
                VirtualHost = _rabbitMQSettings.VirtualHost
            };
        }

        public async Task<IConnection> CreateConnectionAsync()
        {
            if (_connection == null || !_connection.IsOpen)
            {
                _connection = await _factory.CreateConnectionAsync();
            }
            return _connection;
        }
    }
}
