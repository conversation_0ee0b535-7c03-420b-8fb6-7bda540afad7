﻿using DWH.ProjectServices.API.Infrastructure.RabbitMQ.Constants;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ.Interfaces;
using DWH.ProjectServices.API.Models;
using DWH.ProjectServices.API.Models.Interfaces;
using Microsoft.AspNetCore.Http.HttpResults;
using RabbitMQ.Client;
using System;
using System.Text.Json;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;


namespace DWH.ProjectServices.API.Infrastructure.RabbitMQ
{
    public class RabbitMQSender : IRabbitMQSender, IAsyncDisposable
    {
        private readonly ILogger<RabbitMQSender> _logger;
        private readonly IConnection _connection;
        private readonly IChannel _channel;

        public RabbitMQSender(IRabbitMQConnectionFactory connectionFactory, ILogger<RabbitMQSender> logger)
        {
            _logger = logger;
            if (connectionFactory == null)
            {
                throw new ArgumentNullException(nameof(connectionFactory));
            }
            _connection = connectionFactory.CreateConnectionAsync().GetAwaiter().GetResult();
            _channel = _connection.CreateChannelAsync().GetAwaiter().GetResult();
        }

        public async Task SendToRabbitMQ(string exchangeName, string routingKey, IProjectServicesData data)
        {
            try
            {

                ArgumentException.ThrowIfNullOrEmpty(exchangeName, nameof(exchangeName));
                ArgumentException.ThrowIfNullOrEmpty(routingKey, nameof(routingKey));
                // Validate data object
                ArgumentNullException.ThrowIfNull(data, nameof(data));

                ArgumentNullException.ThrowIfNull(data.SyncingEntityId, nameof(data));
                byte[] bodyMsg;
                if (routingKey == RMQConstants.RetailerSeparationProjectServicesRoutingKey && exchangeName == RetailerSeparationConstants.RetailerSeparationExchange)
                {
                    bodyMsg = JsonSerializer.SerializeToUtf8Bytes((RetailerSeparationsData)data);
                }
                else if (routingKey == RMQConstants.ProjectServicesSecurityRoutingKey && exchangeName == ProjectSecurityConstants.ProjectServicesSecurityExchange)
                {
                    bodyMsg = JsonSerializer.SerializeToUtf8Bytes((RetailerSeparationsSecurityData)data);
                }
                else
                {
                    bodyMsg = JsonSerializer.SerializeToUtf8Bytes(data);
                }

                _channel.BasicPublishAsync(exchangeName, routingKey, bodyMsg);
                _logger.LogDebug("Message sent to RabbitMQ. Exchange: {Exchange}, RoutingKey: {RoutingKey}, Data: {Data}", exchangeName, routingKey, new { RabbitMQ = new { Exchange = exchangeName, RoutingKey = routingKey, Data = JsonSerializer.Serialize(data) } });
                _logger.LogInformation("Message sent to RabbitMQ. Exchange: {Exchange}, RoutingKey: {RoutingKey}, Data: {Data}", exchangeName, routingKey, new { RabbitMQ = new { Exchange = exchangeName, RoutingKey = routingKey, Data = JsonSerializer.Serialize(data) } });
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error sending message to RabbitMQ: {ex.Message}", ex);
                throw;
            }
        }


        public async ValueTask DisposeAsync()
        {
            if (_channel != null)
            {
                await _channel.CloseAsync();
            }

            if (_connection != null)
            {
                await _connection.CloseAsync();
            }
        }
    }
}
