﻿using Microsoft.AspNetCore.SignalR;
using System.Collections.Concurrent;
using System.Threading.Tasks;

namespace DWH.ProjectServices.API.Infrastructure.SignalR
{
    using System.Collections.Concurrent;
    using Microsoft.AspNetCore.SignalR;

    public class NotificationHub : Hub
    {
        private static readonly ConcurrentDictionary<string, string> _connectedUsers = new();

        public override async Task OnConnectedAsync()
        {
            var httpContext = Context.GetHttpContext();
            Console.WriteLine($"[SignalR] User connected with ConnectionId: {Context.ConnectionId}");
            var username = httpContext?.Request.Query["username"].ToString();

            if (!string.IsNullOrEmpty(username))
            {
                _connectedUsers.TryAdd(username, Context.ConnectionId);
            }

            await base.OnConnectedAsync();
        }

        public override async Task OnDisconnectedAsync(Exception exception)
        {
            var user = _connectedUsers.FirstOrDefault(x => x.Value == Context.ConnectionId);
            if (!string.IsNullOrEmpty(user.Key))
            {
                _connectedUsers.TryRemove(user.Key, out _);
            }
            await base.OnDisconnectedAsync(exception);
        }

        public async Task SendNotificationToUser(string username, string message)
        {
            if (_connectedUsers.TryGetValue(username, out string connectionId))
            {
                await Clients.Client(connectionId).SendAsync("ReceiveNotification", message);
            }
        }
        public static string GetConnectionId(string username)
        {
            return _connectedUsers.TryGetValue(username, out var connectionId) ? connectionId : null;
        }

    }

}