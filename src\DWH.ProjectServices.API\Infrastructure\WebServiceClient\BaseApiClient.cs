﻿using Polly;
using Polly.CircuitBreaker;
using Polly.Retry;
using Polly.Wrap;
using Newtonsoft.Json;
using System.Net;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;
using Microsoft.Extensions.Logging;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using DWH.ProjectServices.API.Services.Helper.Interface;
using DWH.ProjectServices.API.Infrastructure.WebServiceClient.Interfaces;

namespace DWH.ProjectServices.API.Infrastructure.WebServiceClient
{
    public abstract class BaseApiClient
    {
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly ITokenService _tokenService;
        private readonly ILogger _logger;
        private readonly IPollyPolicyHelper _pollyHelper;
        private string _token;
        private DateTime _tokenExpiry = DateTime.MinValue;
        private readonly SemaphoreSlim _tokenLock = new(1, 1);


        protected BaseApiClient(IHttpClientFactory httpClientFactory, ITokenService tokenService, ILogger logger, IPollyPolicyHelper pollyHelper)
        {
            _httpClientFactory = httpClientFactory;
            _tokenService = tokenService;
            _logger = logger;
            _pollyHelper = pollyHelper;
        }

        protected async Task<HttpClient> GetHttpClientAsync(string clientName, string tokenServiceName)
        {
            await EnsureValidTokenAsync(tokenServiceName);

            var client = _httpClientFactory.CreateClient(clientName);
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", _token);
            return client;
        }

        private async Task EnsureValidTokenAsync(string tokenServiceName)
        {
            if (_token == null || DateTime.UtcNow >= _tokenExpiry)
            {
                await _tokenLock.WaitAsync();
                try
                {
                    // Double-check inside the lock to prevent multiple refreshes in multi-threaded scenarios
                    if (_token == null || DateTime.UtcNow >= _tokenExpiry)
                    {
                        var tokenResponse = await _tokenService.GetTokenAsync(tokenServiceName);

                        _token = tokenResponse.AccessToken;
                        _tokenExpiry = DateTime.UtcNow.AddSeconds(tokenResponse.ExpiresIn - 60); // Refresh 1 min before expiry
                    }
                }
                finally
                {
                    _tokenLock.Release();
                }
            }
        }


        protected AsyncRetryPolicy<ServiceResponse<T>> GetRetryPolicy<T>()
        {
            return _pollyHelper.GetRetryPolicy<T>();
        }

        protected AsyncCircuitBreakerPolicy<ServiceResponse<T>> GetCircuitBreakerPolicy<T>()
        {
            return _pollyHelper.GetCircuitBreakerPolicy<T>();
        }

        protected async Task<ServiceResponse<T>> ExecuteWithPoliciesAsync<T>(
            Func<Task<HttpResponseMessage>> action,
            string requestUri)
        {
            var retryPolicy = GetRetryPolicy<T>();
            var circuitBreakerPolicy = GetCircuitBreakerPolicy<T>();
            var policyWrap = Policy.WrapAsync(retryPolicy, circuitBreakerPolicy);

            return await policyWrap.ExecuteAsync(async () =>
            {
                try
                {
                    var httpResponse = await action();
                    var isSuccess = httpResponse.IsSuccessStatusCode;
                    var jsonResponse = await httpResponse.Content.ReadAsStringAsync();
                    var data = isSuccess ? JsonConvert.DeserializeObject<T>(jsonResponse) : default;
                    var errorMessage = isSuccess ? null : $"Error: {httpResponse.StatusCode} - {httpResponse.ReasonPhrase}";

                    return new ServiceResponse<T>(
                        Data: data,
                        IsSuccess: isSuccess,
                        ErrorMessage: errorMessage,
                        StatusCode: httpResponse.StatusCode,
                        ReasonPhrase: httpResponse.ReasonPhrase);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "An error occurred during the request for {RequestUri}.", requestUri);
                    return new ServiceResponse<T>(
                        Data: default,
                        IsSuccess: false,
                        ErrorMessage: ex.Message,
                        StatusCode: HttpStatusCode.InternalServerError,
                        ReasonPhrase: "Internal Server Error");
                }
            });
        }
    }
}
