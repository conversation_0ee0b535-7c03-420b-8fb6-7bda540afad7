﻿using System.Net.Http;
using System.Threading.Tasks;
using DWH.ProjectServices.API.Infrastructure.WebServiceClient.Interfaces;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;
using DWH.ProjectServices.API.Services.Constants;
using DWH.ProjectServices.API.Services.Helper.Interface;
using Microsoft.Extensions.Logging;

namespace DWH.ProjectServices.API.Infrastructure.WebServiceClient
{
    public class DateApiClient : BaseApiClient, IDateApiClient
    {
        public DateApiClient(
            IHttpClientFactory httpClientFactory,
            ITokenService tokenService,
            ILogger<DateApiClient> logger,
            IPollyPolicyHelper pollyHelper)
            : base(httpClientFactory, tokenService, logger, pollyHelper)
        {
        }

        public async Task<ServiceResponse<T>> GetAsync<T>(string requestUri)
        {
            var client = await GetHttpClientAsync("DateApi", AppConstants.DateAPI);
            return await ExecuteWithPoliciesAsync<T>(() => client.GetAsync(requestUri), requestUri);
        }

        public async Task<ServiceResponse<T>> PostAsync<T>(string requestUri, StringContent requestContent)
        {
            var client = await GetHttpClientAsync("DateApi", AppConstants.DateAPI);
            return await ExecuteWithPoliciesAsync<T>(() => client.PostAsync(requestUri, requestContent), requestUri);
        }
    }
}
