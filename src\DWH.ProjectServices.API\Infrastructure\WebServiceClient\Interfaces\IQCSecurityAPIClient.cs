﻿using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;

namespace DWH.ProjectServices.API.Infrastructure.WebServiceClient.Interfaces
{
    public interface IQCSecurityAPIClient
    {

            Task<ServiceResponse<T>> PostAsync<T>(string requestUri, StringContent requestContent,string userName);

            Task<ServiceResponse<T>> PostAsync<T>(string requestUri, StringContent requestContent, string userName, string countryIds);

            Task<ServiceResponse<T>> GetAsync<T>(string requestUri);

    }
}
