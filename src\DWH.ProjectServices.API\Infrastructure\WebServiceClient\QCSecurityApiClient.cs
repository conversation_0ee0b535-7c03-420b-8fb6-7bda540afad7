﻿using DWH.ProjectServices.API.Infrastructure.WebServiceClient.Interfaces;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;
using DWH.ProjectServices.API.Services.Constants;
using DWH.ProjectServices.API.Services.Helper.Interface;

namespace DWH.ProjectServices.API.Infrastructure.WebServiceClient
{
    

        public class QCSecurityApiClient : BaseApiClient, IQCSecurityAPIClient
        {
            public QCSecurityApiClient(
                IHttpClientFactory httpClientFactory,
                ITokenService tokenService,
                ILogger<JiraAPIClient> logger,
                IPollyPolicyHelper pollyHelper)
                : base(httpClientFactory, tokenService, logger, pollyHelper)
            {
            }

            public async Task<ServiceResponse<T>> GetAsync<T>(string requestUri)
            {
                var client = await GetHttpClientAsync("QCSecurityAPI", AppConstants.BuilderAPI);
                return await ExecuteWithPoliciesAsync<T>(() => client.GetAsync(requestUri), requestUri);
            }

            public async Task<ServiceResponse<T>> PostAsync<T>(string requestUri, StringContent requestContent,string userName)
            {
                var client = await GetHttpClientAsync("QCSecurityAPI", AppConstants.BuilderAPI);
                var requestMessage = new HttpRequestMessage(HttpMethod.Post, requestUri)
            {
                Content = requestContent
            };
            requestMessage.Headers.Add("userName", userName);
            return await ExecuteWithPoliciesAsync<T>(() => client.SendAsync(requestMessage), requestUri);

        }

            public async Task<ServiceResponse<T>> PostAsync<T>(string requestUri, StringContent requestContent, string userName, string countryIds)
            {
                var client = await GetHttpClientAsync("QCSecurityAPI", AppConstants.BuilderAPI);
                var requestMessage = new HttpRequestMessage(HttpMethod.Post, requestUri)
                {
                    Content = requestContent
                };
                requestMessage.Headers.Add("userName", userName);
                if (!string.IsNullOrEmpty(countryIds))
                {
                    requestMessage.Headers.Add("Custom-Countryid", countryIds);
                }
                return await ExecuteWithPoliciesAsync<T>(() => client.SendAsync(requestMessage), requestUri);
            }
        

    }
}


