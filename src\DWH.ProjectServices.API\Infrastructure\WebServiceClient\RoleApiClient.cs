﻿using DWH.ProjectServices.API.Infrastructure.WebServiceClient.Interfaces;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;
using DWH.ProjectServices.API.Services.Constants;
using DWH.ProjectServices.API.Services.Helper.Interface;

namespace DWH.ProjectServices.API.Infrastructure.WebServiceClient
{
    public class RoleApiClient : BaseApiClient, IRoleApiClient
    {
        public RoleApiClient(
            IHttpClientFactory httpClientFactory,
            ITokenService tokenService,
            ILogger<RoleApiClient> logger,
            IPollyPolicyHelper pollyHelper)
            : base(httpClientFactory, tokenService, logger, pollyHelper)
        {
        }

        public async Task<ServiceResponse<T>> GetAsync<T>(string requestUri)
        {
            var client = await GetHttpClientAsync("RoleApi", AppConstants.BuilderAPI);
            return await ExecuteWithPoliciesAsync<T>(() => client.GetAsync(requestUri), requestUri);
        }
    }
}
