﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace DWH.ProjectServices.API.Migrations
{
    /// <inheritdoc />
    public partial class AddedQCProjectsandPeriods : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<DateTime>(
                name: "CreatedWhen",
                table: "BaseProjects",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(2023, 12, 26, 8, 35, 56, 960, DateTimeKind.Utc).AddTicks(5629),
                oldClrType: typeof(DateTime),
                oldType: "timestamp with time zone",
                oldDefaultValue: new DateTime(2023, 11, 22, 6, 27, 8, 650, DateTimeKind.Utc).AddTicks(2215));

            migrationBuilder.CreateTable(
                name: "ResetCorrectionTypes",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Name = table.Column<string>(type: "character varying(40)", maxLength: 40, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ResetCorrectionTypes", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "QCProjects",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    BaseProjectId = table.Column<int>(type: "integer", nullable: false),
                    ResetCorrectionTypeId = table.Column<int>(type: "integer", nullable: true),
                    IsAutoLoad = table.Column<bool>(type: "boolean", nullable: false),
                    IsQCEnable = table.Column<bool>(type: "boolean", nullable: false),
                    IsAutomatedPriceCheck = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_QCProjects", x => x.Id);
                    table.ForeignKey(
                        name: "FK_QCProjects_BaseProjects_BaseProjectId",
                        column: x => x.BaseProjectId,
                        principalTable: "BaseProjects",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_QCProjects_ResetCorrectionTypes_ResetCorrectionTypeId",
                        column: x => x.ResetCorrectionTypeId,
                        principalTable: "ResetCorrectionTypes",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "QCPeriod",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    QCProjectId = table.Column<int>(type: "integer", nullable: false),
                    PeriodId = table.Column<int>(type: "integer", nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: true),
                    CreatedBy = table.Column<int>(type: "integer", nullable: false),
                    CreatedWhen = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false, defaultValue: new DateTimeOffset(new DateTime(2023, 12, 26, 8, 35, 56, 960, DateTimeKind.Unspecified).AddTicks(6817), new TimeSpan(0, 0, 0, 0, 0))),
                    UpdatedBy = table.Column<int>(type: "integer", nullable: true),
                    UpdatedWhen = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_QCPeriod", x => x.Id);
                    table.ForeignKey(
                        name: "FK_QCPeriod_QCProjects_QCProjectId",
                        column: x => x.QCProjectId,
                        principalTable: "QCProjects",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Period",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    RefProjectId = table.Column<int>(type: "integer", nullable: true),
                    RefPeriodId = table.Column<int>(type: "integer", nullable: true),
                    QCPeriodId = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Period", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Period_QCPeriod_QCPeriodId",
                        column: x => x.QCPeriodId,
                        principalTable: "QCPeriod",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "StockInitialization",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    StockBaseProjectId = table.Column<int>(type: "integer", nullable: false),
                    StockPeriodId = table.Column<int>(type: "integer", nullable: true),
                    QCPeriodId = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_StockInitialization", x => x.Id);
                    table.ForeignKey(
                        name: "FK_StockInitialization_QCPeriod_QCPeriodId",
                        column: x => x.QCPeriodId,
                        principalTable: "QCPeriod",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Period_QCPeriodId",
                table: "Period",
                column: "QCPeriodId");

            migrationBuilder.CreateIndex(
                name: "IX_QCPeriod_QCProjectId",
                table: "QCPeriod",
                column: "QCProjectId");

            migrationBuilder.CreateIndex(
                name: "IX_QCProjects_BaseProjectId",
                table: "QCProjects",
                column: "BaseProjectId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_QCProjects_ResetCorrectionTypeId",
                table: "QCProjects",
                column: "ResetCorrectionTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_StockInitialization_QCPeriodId",
                table: "StockInitialization",
                column: "QCPeriodId",
                unique: true);


            migrationBuilder.InsertData(
                table: "ResetCorrectionTypes",
                columns: new[] { "Id", "Name" },
                values: new object[,]
                {
                     { "1", "None" },
                     { "2", "Price" },
                     { "3", "Quantity" },
                     { "4", "Price & Quantity" },
                     { "5", "Quantity & Price (Original > 0)" }
                });


        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Period");

            migrationBuilder.DropTable(
                name: "StockInitialization");

            migrationBuilder.DropTable(
                name: "QCPeriod");

            migrationBuilder.DropTable(
                name: "QCProjects");

            migrationBuilder.DropTable(
                name: "ResetCorrectionTypes");

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreatedWhen",
                table: "BaseProjects",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(2023, 11, 22, 6, 27, 8, 650, DateTimeKind.Utc).AddTicks(2215),
                oldClrType: typeof(DateTime),
                oldType: "timestamp with time zone",
                oldDefaultValue: new DateTime(2023, 12, 26, 8, 35, 56, 960, DateTimeKind.Utc).AddTicks(5629));
        }
    }
}
