﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DWH.ProjectServices.API.Migrations
{
    /// <inheritdoc />
    public partial class UpdatePeriodIdTypeQCPeriod : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<long>(
                name: "PeriodId",
                table: "QCPeriod",
                type: "bigint",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "integer");

            migrationBuilder.AlterColumn<DateTimeOffset>(
                name: "CreatedWhen",
                table: "QCPeriod",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTimeOffset(new DateTime(2024, 1, 9, 3, 34, 41, 253, DateTimeKind.Unspecified).AddTicks(6297), new TimeSpan(0, 0, 0, 0, 0)),
                oldClrType: typeof(DateTimeOffset),
                oldType: "timestamp with time zone",
                oldDefaultValue: new DateTimeOffset(new DateTime(2024, 1, 8, 9, 38, 0, 41, DateTimeKind.Unspecified).AddTicks(5002), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreatedWhen",
                table: "BaseProjects",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(2024, 1, 9, 3, 34, 41, 253, DateTimeKind.Utc).AddTicks(5198),
                oldClrType: typeof(DateTime),
                oldType: "timestamp with time zone",
                oldDefaultValue: new DateTime(2024, 1, 8, 9, 38, 0, 41, DateTimeKind.Utc).AddTicks(4076));
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<int>(
                name: "PeriodId",
                table: "QCPeriod",
                type: "integer",
                nullable: false,
                oldClrType: typeof(long),
                oldType: "bigint");

            migrationBuilder.AlterColumn<DateTimeOffset>(
                name: "CreatedWhen",
                table: "QCPeriod",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTimeOffset(new DateTime(2024, 1, 8, 9, 38, 0, 41, DateTimeKind.Unspecified).AddTicks(5002), new TimeSpan(0, 0, 0, 0, 0)),
                oldClrType: typeof(DateTimeOffset),
                oldType: "timestamp with time zone",
                oldDefaultValue: new DateTimeOffset(new DateTime(2024, 1, 9, 3, 34, 41, 253, DateTimeKind.Unspecified).AddTicks(6297), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreatedWhen",
                table: "BaseProjects",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(2024, 1, 8, 9, 38, 0, 41, DateTimeKind.Utc).AddTicks(4076),
                oldClrType: typeof(DateTime),
                oldType: "timestamp with time zone",
                oldDefaultValue: new DateTime(2024, 1, 9, 3, 34, 41, 253, DateTimeKind.Utc).AddTicks(5198));
        }
    }
}
