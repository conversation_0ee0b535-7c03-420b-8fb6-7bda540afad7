﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DWH.ProjectServices.API.Migrations
{
    /// <inheritdoc />
    public partial class AddIndexForPeriods : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<DateTimeOffset>(
                name: "CreatedWhen",
                table: "QCPeriod",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTimeOffset(new DateTime(2024, 1, 12, 7, 38, 40, 1, DateTimeKind.Unspecified).AddTicks(6823), new TimeSpan(0, 0, 0, 0, 0)),
                oldClrType: typeof(DateTimeOffset),
                oldType: "timestamp with time zone",
                oldDefaultValue: new DateTimeOffset(new DateTime(2024, 1, 9, 3, 34, 41, 253, DateTimeKind.Unspecified).AddTicks(6297), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.AddColumn<int>(
                name: "index",
                table: "Period",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreatedWhen",
                table: "BaseProjects",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(2024, 1, 12, 7, 38, 40, 1, DateTimeKind.Utc).AddTicks(5758),
                oldClrType: typeof(DateTime),
                oldType: "timestamp with time zone",
                oldDefaultValue: new DateTime(2024, 1, 9, 3, 34, 41, 253, DateTimeKind.Utc).AddTicks(5198));
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "index",
                table: "Period");

            migrationBuilder.AlterColumn<DateTimeOffset>(
                name: "CreatedWhen",
                table: "QCPeriod",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTimeOffset(new DateTime(2024, 1, 9, 3, 34, 41, 253, DateTimeKind.Unspecified).AddTicks(6297), new TimeSpan(0, 0, 0, 0, 0)),
                oldClrType: typeof(DateTimeOffset),
                oldType: "timestamp with time zone",
                oldDefaultValue: new DateTimeOffset(new DateTime(2024, 1, 12, 7, 38, 40, 1, DateTimeKind.Unspecified).AddTicks(6823), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreatedWhen",
                table: "BaseProjects",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(2024, 1, 9, 3, 34, 41, 253, DateTimeKind.Utc).AddTicks(5198),
                oldClrType: typeof(DateTime),
                oldType: "timestamp with time zone",
                oldDefaultValue: new DateTime(2024, 1, 12, 7, 38, 40, 1, DateTimeKind.Utc).AddTicks(5758));
        }
    }
}
