﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace DWH.ProjectServices.API.Migrations
{
    /// <inheritdoc />
    public partial class AddNullableFieldForStockInitialization : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<int>(
                name: "StockBaseProjectId",
                table: "StockInitialization",
                type: "integer",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "integer");



        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<int>(
                name: "StockBaseProjectId",
                table: "StockInitialization",
                type: "integer",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "integer",
                oldNullable: true);






        }
    }
}
