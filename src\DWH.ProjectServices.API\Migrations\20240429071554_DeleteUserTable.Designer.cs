﻿// <auto-generated />
using System;
using DWH.ProjectServices.API.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace DWH.ProjectServices.API.Migrations
{
    [DbContext(typeof(PostgreSqlDbContext))]
    [Migration("20240429071554_DeleteUserTable")]
    partial class DeleteUserTable
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "7.0.9")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("DWH.ProjectServices.API.Models.BaseProject", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("CountryId")
                        .HasColumnType("integer");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedWhen")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValue(new DateTime(2024, 4, 29, 7, 15, 53, 945, DateTimeKind.Utc).AddTicks(76));

                    b.Property<bool?>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("DeletedWhen")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsRelevantForReportingEnabled")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .HasMaxLength(40)
                        .HasColumnType("character varying(40)");

                    b.Property<int>("PanelId")
                        .HasColumnType("integer");

                    b.Property<int>("PeriodicityId")
                        .HasColumnType("integer");

                    b.Property<int>("TypeId")
                        .HasColumnType("integer");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("UpdatedWhen")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.ToTable("BaseProjects");
                });

            modelBuilder.Entity("DWH.ProjectServices.API.Models.BaseProjectPanel", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Name")
                        .HasMaxLength(40)
                        .HasColumnType("character varying(40)");

                    b.HasKey("Id");

                    b.ToTable("BaseProject_Panels");
                });

            modelBuilder.Entity("DWH.ProjectServices.API.Models.BaseProjectPredecessor", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("BaseProjectId")
                        .HasColumnType("integer");

                    b.Property<int>("PredecessorId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("BaseProjectId");

                    b.ToTable("BaseProject_Predecessors");
                });

            modelBuilder.Entity("DWH.ProjectServices.API.Models.BaseProjectProductGroup", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("BaseProjectId")
                        .HasColumnType("integer");

                    b.Property<bool?>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<int>("ProductGroupId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("BaseProjectId");

                    b.ToTable("BaseProject_ProductGroups");
                });

            modelBuilder.Entity("DWH.ProjectServices.API.Models.Period", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<long>("QCPeriodId")
                        .HasColumnType("bigint");

                    b.Property<long?>("RefPeriodId")
                        .HasColumnType("bigint");

                    b.Property<int?>("RefProjectId")
                        .HasColumnType("integer");

                    b.Property<int>("index")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("QCPeriodId");

                    b.ToTable("Period");
                });

            modelBuilder.Entity("DWH.ProjectServices.API.Models.QCPeriod", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTimeOffset>("CreatedWhen")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValue(new DateTimeOffset(new DateTime(2024, 4, 29, 7, 15, 53, 945, DateTimeKind.Unspecified).AddTicks(625), new TimeSpan(0, 0, 0, 0, 0)));

                    b.Property<long>("PeriodId")
                        .HasColumnType("bigint");

                    b.Property<int>("QCProjectId")
                        .HasColumnType("integer");

                    b.Property<int?>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<DateTimeOffset?>("UpdatedWhen")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("QCProjectId");

                    b.ToTable("QCPeriod");
                });

            modelBuilder.Entity("DWH.ProjectServices.API.Models.QCProject", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("BaseProjectId")
                        .HasColumnType("integer");

                    b.Property<bool?>("IsAutoLoad")
                        .HasColumnType("boolean");

                    b.Property<bool?>("IsAutomatedPriceCheck")
                        .HasColumnType("boolean");

                    b.Property<bool?>("IsQCEnable")
                        .HasColumnType("boolean");

                    b.Property<int?>("ResetCorrectionTypeId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("BaseProjectId")
                        .IsUnique();

                    b.HasIndex("ResetCorrectionTypeId");

                    b.ToTable("QCProjects");
                });

            modelBuilder.Entity("DWH.ProjectServices.API.Models.ResetCorrectionType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Name")
                        .HasMaxLength(40)
                        .HasColumnType("character varying(40)");

                    b.HasKey("Id");

                    b.ToTable("ResetCorrectionTypes");
                });

            modelBuilder.Entity("DWH.ProjectServices.API.Models.StockInitialization", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<long>("QCPeriodId")
                        .HasColumnType("bigint");

                    b.Property<int?>("StockBaseProjectId")
                        .HasColumnType("integer");

                    b.Property<long?>("StockPeriodId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("QCPeriodId")
                        .IsUnique();

                    b.ToTable("StockInitialization");
                });

            modelBuilder.Entity("DWH.ProjectServices.API.Models.BaseProjectPredecessor", b =>
                {
                    b.HasOne("DWH.ProjectServices.API.Models.BaseProject", null)
                        .WithMany("Predecessors")
                        .HasForeignKey("BaseProjectId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("DWH.ProjectServices.API.Models.BaseProjectProductGroup", b =>
                {
                    b.HasOne("DWH.ProjectServices.API.Models.BaseProject", null)
                        .WithMany("ProductGroups")
                        .HasForeignKey("BaseProjectId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("DWH.ProjectServices.API.Models.Period", b =>
                {
                    b.HasOne("DWH.ProjectServices.API.Models.QCPeriod", null)
                        .WithMany("Periods")
                        .HasForeignKey("QCPeriodId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("DWH.ProjectServices.API.Models.QCPeriod", b =>
                {
                    b.HasOne("DWH.ProjectServices.API.Models.QCProject", null)
                        .WithMany("QCPeriods")
                        .HasForeignKey("QCProjectId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("DWH.ProjectServices.API.Models.QCProject", b =>
                {
                    b.HasOne("DWH.ProjectServices.API.Models.BaseProject", null)
                        .WithOne("QCProjects")
                        .HasForeignKey("DWH.ProjectServices.API.Models.QCProject", "BaseProjectId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("DWH.ProjectServices.API.Models.ResetCorrectionType", null)
                        .WithOne("QCProject")
                        .HasForeignKey("DWH.ProjectServices.API.Models.QCProject", "ResetCorrectionTypeId");
                });

            modelBuilder.Entity("DWH.ProjectServices.API.Models.StockInitialization", b =>
                {
                    b.HasOne("DWH.ProjectServices.API.Models.QCPeriod", null)
                        .WithOne("StockInitialization")
                        .HasForeignKey("DWH.ProjectServices.API.Models.StockInitialization", "QCPeriodId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("DWH.ProjectServices.API.Models.BaseProject", b =>
                {
                    b.Navigation("Predecessors");

                    b.Navigation("ProductGroups");

                    b.Navigation("QCProjects");
                });

            modelBuilder.Entity("DWH.ProjectServices.API.Models.QCPeriod", b =>
                {
                    b.Navigation("Periods");

                    b.Navigation("StockInitialization");
                });

            modelBuilder.Entity("DWH.ProjectServices.API.Models.QCProject", b =>
                {
                    b.Navigation("QCPeriods");
                });

            modelBuilder.Entity("DWH.ProjectServices.API.Models.ResetCorrectionType", b =>
                {
                    b.Navigation("QCProject");
                });
#pragma warning restore 612, 618
        }
    }
}
