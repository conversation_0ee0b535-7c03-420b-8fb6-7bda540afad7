﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
#nullable disable
namespace DWH.ProjectServices.API.Migrations
{
    /// <inheritdoc />
    public partial class UpdateSQCMode : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Add the new column
            migrationBuilder.AddColumn<int>(
                name: "SQCMode",
                table: "QCProjects",
                type: "integer",
                nullable: true);

            // Map the values from IsQCEnable to SQCMode
            UpdatesSQCMode(migrationBuilder);

            // Drop the old column
            migrationBuilder.DropColumn(
                name: "IsQCEnable",
                table: "QCProjects");

     
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Add the old column back
            migrationBuilder.AddColumn<bool>(
                name: "IsQCEnable",
                table: "QCProjects",
                type: "boolean",
                nullable: true);

            // Reverse the mapping
            RevertSQCMode(migrationBuilder);

            // Drop the new column
            migrationBuilder.DropColumn(
                name: "SQCMode",
                table: "QCProjects");

         
        }

        private static void UpdatesSQCMode(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(
                @"UPDATE ""QCProjects""
                  SET ""SQCMode"" = CASE 
                                      WHEN ""IsQCEnable"" = TRUE THEN 1 
                                      WHEN ""IsQCEnable"" = FALSE THEN 0 
                                      ELSE NULL 
                                    END");
        }

        private static void RevertSQCMode(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(
                @"UPDATE ""QCProjects""
                  SET ""IsQCEnable"" = CASE 
                                         WHEN ""SQCMode"" = 1 THEN TRUE 
                                         WHEN ""SQCMode"" = 0 THEN FALSE 
                                         ELSE NULL 
                                       END");
        }

  
    }
}
