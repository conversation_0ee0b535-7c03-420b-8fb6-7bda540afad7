﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace DWH.ProjectServices.API.Migrations
{
    /// <inheritdoc />
    public partial class BaseProjectPanelTypePurpose : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "BaseProject_PanelType",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Name = table.Column<string>(type: "character varying(40)", maxLength: 40, nullable: true),
                    Desc = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BaseProject_PanelType", x => x.Id);
                });

            migrationBuilder.InsertData(
               table: "BaseProject_PanelType",
               columns: new[] { "Id", "Name", "Desc" },
               values: new object[,]
               {
                    { "1", "FW", "Full Weekly Extrapolated"},
                    { "2", "WC", "Weekly Channels"},
                    { "3", "MC", "Monthly Channels"},
                    { "4", "MP", "Marketplace data"},
                    { "5", "LPAC", "Leader Panel (non-extrapolated) - Additional channels loaded from IOP"},
                    { "6", "LPWC", "Leader Panel (non-extrapolated) - Autoload from WC BPs"},
                    { "7", "LP", "Leader Panel (non-extrapolated)"},
                    { "8", "Unknown", "Unknown"},
                    { "9", "MD", "Modeled Data"},
                    { "10", "WE", "Weekly Extrapolated"},
                    { "11", "ME", "Monthly Extrapolated"},

               });

            migrationBuilder.CreateTable(
                name: "BaseProject_Purpose",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Name = table.Column<string>(type: "character varying(40)", maxLength: 40, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BaseProject_Purpose", x => x.Id);
                });

            migrationBuilder.InsertData(
               table: "BaseProject_Purpose",
               columns: new[] { "Id", "Name" },
               values: new object[,]
               {
                    { "1", "Production" },
                    { "2", "Backup" },
                    { "3", "Test" },
                    { "4", "Training" },
                    { "5", "Unknown" },
                    { "6", "Rework" },
                    { "7", "IRAN Closure" },
                    { "8", "Backdata" },
                    { "9", "Deleted" },
               });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "BaseProject_PanelType");

            migrationBuilder.DropTable(
                name: "BaseProject_Purpose");
        }
    }
}
