﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DWH.ProjectServices.API.Migrations
{
    /// <inheritdoc />
    public partial class PanelTypePurposeinBP : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<DateTimeOffset>(
                name: "Created<PERSON>hen",
                table: "QCPeriod",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTimeOffset(new DateTime(2024, 6, 20, 12, 55, 53, 514, DateTimeKind.Unspecified).AddTicks(9305), new TimeSpan(0, 0, 0, 0, 0)),
                oldClrType: typeof(DateTimeOffset),
                oldType: "timestamp with time zone",
                oldDefaultValue: new DateTimeOffset(new DateTime(2024, 6, 20, 10, 29, 41, 920, DateTimeKind.Unspecified).AddTicks(1409), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.AlterColumn<DateTime>(
                name: "Created<PERSON><PERSON>",
                table: "BaseProjects",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(2024, 6, 20, 12, 55, 53, 514, DateTimeKind.Utc).AddTicks(8413),
                oldClrType: typeof(DateTime),
                oldType: "timestamp with time zone",
                oldDefaultValue: new DateTime(2024, 6, 20, 10, 29, 41, 920, DateTimeKind.Utc).AddTicks(324));

            migrationBuilder.AddColumn<int>(
                name: "PanelTypeId",
                table: "BaseProjects",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "PurposeId",
                table: "BaseProjects",
                type: "integer",
                nullable: false,
                defaultValue: 0);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "PanelTypeId",
                table: "BaseProjects");

            migrationBuilder.DropColumn(
                name: "PurposeId",
                table: "BaseProjects");

            migrationBuilder.AlterColumn<DateTimeOffset>(
                name: "CreatedWhen",
                table: "QCPeriod",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTimeOffset(new DateTime(2024, 6, 20, 10, 29, 41, 920, DateTimeKind.Unspecified).AddTicks(1409), new TimeSpan(0, 0, 0, 0, 0)),
                oldClrType: typeof(DateTimeOffset),
                oldType: "timestamp with time zone",
                oldDefaultValue: new DateTimeOffset(new DateTime(2024, 6, 20, 12, 55, 53, 514, DateTimeKind.Unspecified).AddTicks(9305), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.AlterColumn<DateTime>(
                name: "CreatedWhen",
                table: "BaseProjects",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTime(2024, 6, 20, 10, 29, 41, 920, DateTimeKind.Utc).AddTicks(324),
                oldClrType: typeof(DateTime),
                oldType: "timestamp with time zone",
                oldDefaultValue: new DateTime(2024, 6, 20, 12, 55, 53, 514, DateTimeKind.Utc).AddTicks(8413));
        }
    }
}
