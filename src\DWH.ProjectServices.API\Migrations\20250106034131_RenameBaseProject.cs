﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace DWH.ProjectServices.API.Migrations
{
    /// <inheritdoc />
    public partial class RenameBaseProject : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_BaseProject_Predecessors_BaseProjects_BaseProjectId",
                table: "BaseProject_Predecessors");

            migrationBuilder.DropForeignKey(
                name: "FK_BaseProject_ProductGroups_BaseProjects_BaseProjectId",
                table: "BaseProject_ProductGroups");

            migrationBuilder.DropForeignKey(
                name: "FK_QCProjects_BaseProjects_BaseProjectId",
                table: "QCProjects");

            migrationBuilder.DropIndex(
                name: "IX_QCProjects_BaseProjectId",
                table: "QCProjects");

            migrationBuilder.DropIndex(
                name: "IX_BaseProject_ProductGroups_BaseProjectId",
                table: "BaseProject_ProductGroups");

            migrationBuilder.DropIndex(
                name: "IX_BaseProject_Predecessors_BaseProjectId",
                table: "BaseProject_Predecessors");

            migrationBuilder.RenameTable(name: "BaseProjects", newName: "BaseProject");

            migrationBuilder.CreateIndex(
                name: "IX_BaseProject_ProductGroups_BaseProjectId",
                table: "BaseProject_ProductGroups",
                column: "BaseProjectId");

            migrationBuilder.CreateIndex(
                name: "IX_BaseProject_Predecessors_BaseProjectId",
                table: "BaseProject_Predecessors",
                column: "BaseProjectId");

            migrationBuilder.CreateIndex(
                name: "IX_BaseProject_PanelTypeId",
                table: "BaseProject",
                column: "PanelTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_BaseProject_PurposeId",
                table: "BaseProject",
                column: "PurposeId");

            migrationBuilder.CreateIndex(
              name: "IX_QCProjects_BaseProjectId",
              table: "QCProjects",
              column: "BaseProjectId",
              unique: true);

            migrationBuilder.AddForeignKey(
                name: "FK_BaseProject_Predecessors_BaseProject_BaseProjectId",
                table: "BaseProject_Predecessors",
                column: "BaseProjectId",
                principalTable: "BaseProject",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_BaseProject_ProductGroups_BaseProject_BaseProjectId",
                table: "BaseProject_ProductGroups",
                column: "BaseProjectId",
                principalTable: "BaseProject",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_BaseProject_QCProjects_BaseProjectId",
                table: "QCProjects",
                column: "BaseProjectId",
                principalTable: "BaseProject",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_BaseProject_Predecessors_BaseProject_BaseProjectEntityId",
                table: "BaseProject_Predecessors");

            migrationBuilder.DropForeignKey(
                name: "FK_BaseProject_ProductGroups_BaseProject_BaseProjectEntityId",
                table: "BaseProject_ProductGroups");

            migrationBuilder.DropTable(
                name: "BaseProject");

            migrationBuilder.DropIndex(
                name: "IX_BaseProject_ProductGroups_BaseProjectEntityId",
                table: "BaseProject_ProductGroups");

            migrationBuilder.DropIndex(
                name: "IX_BaseProject_Predecessors_BaseProjectEntityId",
                table: "BaseProject_Predecessors");

            migrationBuilder.DropColumn(
                name: "BaseProjectEntityId",
                table: "BaseProject_ProductGroups");

            migrationBuilder.DropColumn(
                name: "BaseProjectEntityId",
                table: "BaseProject_Predecessors");

            migrationBuilder.AlterColumn<DateTimeOffset>(
                name: "CreatedWhen",
                table: "QCPeriod",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTimeOffset(new DateTime(2024, 11, 18, 10, 22, 50, 928, DateTimeKind.Unspecified).AddTicks(6274), new TimeSpan(0, 0, 0, 0, 0)),
                oldClrType: typeof(DateTimeOffset),
                oldType: "timestamp with time zone",
                oldDefaultValue: new DateTimeOffset(new DateTime(2025, 1, 6, 3, 41, 30, 191, DateTimeKind.Unspecified).AddTicks(1163), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.CreateTable(
                name: "BaseProjects",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    PanelTypeId = table.Column<int>(type: "integer", nullable: false),
                    PurposeId = table.Column<int>(type: "integer", nullable: false),
                    CountryId = table.Column<int>(type: "integer", nullable: false),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    CreatedWhen = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: false, defaultValue: new DateTimeOffset(new DateTime(2024, 11, 18, 10, 22, 50, 928, DateTimeKind.Unspecified).AddTicks(1387), new TimeSpan(0, 0, 0, 0, 0))),
                    Deleted = table.Column<bool>(type: "boolean", nullable: true),
                    DeletedBy = table.Column<string>(type: "text", nullable: true),
                    DeletedWhen = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    IsRelevantForReportingEnabled = table.Column<bool>(type: "boolean", nullable: false),
                    Name = table.Column<string>(type: "character varying(40)", maxLength: 40, nullable: true),
                    PanelId = table.Column<int>(type: "integer", nullable: false),
                    PeriodicityId = table.Column<int>(type: "integer", nullable: false),
                    TypeId = table.Column<int>(type: "integer", nullable: false),
                    UpdatedBy = table.Column<string>(type: "text", nullable: true),
                    UpdatedWhen = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BaseProjects", x => x.Id);
                    table.ForeignKey(
                        name: "FK_BaseProjects_BaseProject_PanelType_PanelTypeId",
                        column: x => x.PanelTypeId,
                        principalTable: "BaseProject_PanelType",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_BaseProjects_BaseProject_Purpose_PurposeId",
                        column: x => x.PurposeId,
                        principalTable: "BaseProject_Purpose",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_QCProjects_BaseProjectId",
                table: "QCProjects",
                column: "BaseProjectId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_BaseProject_ProductGroups_BaseProjectId",
                table: "BaseProject_ProductGroups",
                column: "BaseProjectId");

            migrationBuilder.CreateIndex(
                name: "IX_BaseProject_Predecessors_BaseProjectId",
                table: "BaseProject_Predecessors",
                column: "BaseProjectId");

            migrationBuilder.CreateIndex(
                name: "IX_BaseProjects_PanelTypeId",
                table: "BaseProjects",
                column: "PanelTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_BaseProjects_PurposeId",
                table: "BaseProjects",
                column: "PurposeId");

            migrationBuilder.AddForeignKey(
                name: "FK_BaseProject_Predecessors_BaseProjects_BaseProjectId",
                table: "BaseProject_Predecessors",
                column: "BaseProjectId",
                principalTable: "BaseProjects",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_BaseProject_ProductGroups_BaseProjects_BaseProjectId",
                table: "BaseProject_ProductGroups",
                column: "BaseProjectId",
                principalTable: "BaseProjects",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_QCProjects_BaseProjects_BaseProjectId",
                table: "QCProjects",
                column: "BaseProjectId",
                principalTable: "BaseProjects",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
