﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace DWH.ProjectServices.API.Migrations
{
    /// <inheritdoc />
    public partial class RenameProdGroupPred : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_BaseProject_Predecessors_BaseProjectId",
                table: "BaseProject_Predecessors");

            migrationBuilder.DropIndex(
                name: "IX_BaseProject_ProductGroups_BaseProjectId",
                table: "BaseProject_ProductGroups");

            migrationBuilder.RenameTable(name: "BaseProject_ProductGroups", newName: "BaseProject_ProductGroup");

            migrationBuilder.RenameTable(name: "BaseProject_Predecessors", newName: "BaseProject_Predecessor");

            migrationBuilder.CreateIndex(
                name: "IX_BaseProject_Predecessor_BaseProjectId",
                table: "BaseProject_Predecessor",
                column: "BaseProjectId");

            migrationBuilder.CreateIndex(
                name: "IX_BaseProject_ProductGroup_BaseProjectId",
                table: "BaseProject_ProductGroup",
                column: "BaseProjectId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_RetailerSeperation_RetailerSeperationRequests_RetailerSeper~",
                table: "RetailerSeperation");

            migrationBuilder.DropForeignKey(
                name: "FK_RetailerSeperationRequestDetail_RetailerSeperationRequests_~",
                table: "RetailerSeperationRequestDetail");

            migrationBuilder.DropTable(
                name: "BaseProject_Predecessor");

            migrationBuilder.DropTable(
                name: "BaseProject_ProductGroup");

            migrationBuilder.DropIndex(
                name: "IX_RetailerSeperationRequestDetail_RetailerSeperationRequestId",
                table: "RetailerSeperationRequestDetail");

            migrationBuilder.DropIndex(
                name: "IX_RetailerSeperation_RetailerSeperationRequestId",
                table: "RetailerSeperation");

            migrationBuilder.AlterColumn<DateTimeOffset>(
                name: "CreatedWhen",
                table: "QCPeriod",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTimeOffset(new DateTime(2025, 1, 6, 12, 55, 20, 607, DateTimeKind.Unspecified).AddTicks(6261), new TimeSpan(0, 0, 0, 0, 0)),
                oldClrType: typeof(DateTimeOffset),
                oldType: "timestamp with time zone",
                oldDefaultValue: new DateTimeOffset(new DateTime(2025, 1, 7, 9, 50, 1, 148, DateTimeKind.Unspecified).AddTicks(642), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.AlterColumn<DateTimeOffset>(
                name: "CreatedWhen",
                table: "BaseProject",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: new DateTimeOffset(new DateTime(2025, 1, 6, 12, 55, 20, 607, DateTimeKind.Unspecified).AddTicks(5518), new TimeSpan(0, 0, 0, 0, 0)),
                oldClrType: typeof(DateTimeOffset),
                oldType: "timestamp with time zone",
                oldDefaultValue: new DateTimeOffset(new DateTime(2025, 1, 7, 9, 50, 1, 147, DateTimeKind.Unspecified).AddTicks(8164), new TimeSpan(0, 0, 0, 0, 0)));

            migrationBuilder.CreateTable(
                name: "BaseProject_Predecessors",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    BaseProjectId = table.Column<int>(type: "integer", nullable: false),
                    PredecessorId = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BaseProject_Predecessors", x => x.Id);
                    table.ForeignKey(
                        name: "FK_BaseProject_Predecessors_BaseProject_BaseProjectId",
                        column: x => x.BaseProjectId,
                        principalTable: "BaseProject",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "BaseProject_ProductGroups",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    BaseProjectId = table.Column<int>(type: "integer", nullable: false),
                    Deleted = table.Column<bool>(type: "boolean", nullable: true),
                    ProductGroupId = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BaseProject_ProductGroups", x => x.Id);
                    table.ForeignKey(
                        name: "FK_BaseProject_ProductGroups_BaseProject_BaseProjectId",
                        column: x => x.BaseProjectId,
                        principalTable: "BaseProject",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_BaseProject_Predecessors_BaseProjectId",
                table: "BaseProject_Predecessors",
                column: "BaseProjectId");

            migrationBuilder.CreateIndex(
                name: "IX_BaseProject_ProductGroups_BaseProjectId",
                table: "BaseProject_ProductGroups",
                column: "BaseProjectId");

            migrationBuilder.AddForeignKey(
                name: "FK_RetailerSeperation_RetailerSeperationRequest_RetailerSepera~",
                table: "RetailerSeperation",
                column: "RetailerSeperationRequestId",
                principalTable: "RetailerSeperationRequest",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_RetailerSeperationRequestDetail_RetailerSeperationRequest_R~",
                table: "RetailerSeperationRequestDetail",
                column: "RetailerSeperationRequestId",
                principalTable: "RetailerSeperationRequest",
                principalColumn: "Id");
        }
    }
}
