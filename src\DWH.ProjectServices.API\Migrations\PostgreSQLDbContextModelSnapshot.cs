﻿// <auto-generated />
using System;
using DWH.ProjectServices.API.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace DWH.ProjectServices.API.Migrations
{
    [DbContext(typeof(PostgreSqlDbContext))]
    partial class PostgreSqlDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.11")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("DWH.ProjectServices.API.Infrastructure.Persistence.Entities.BaseProjectDataType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Desc")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .HasMaxLength(40)
                        .HasColumnType("character varying(40)");

                    b.HasKey("Id");

                    b.ToTable("BaseProject_DataType");
                });

            modelBuilder.Entity("DWH.ProjectServices.API.Infrastructure.Persistence.Entities.BaseProjectEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("CountryId")
                        .HasColumnType("integer");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTimeOffset>("CreatedWhen")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValue(new DateTimeOffset(new DateTime(2025, 5, 16, 15, 23, 28, 867, DateTimeKind.Unspecified).AddTicks(2428), new TimeSpan(0, 0, 0, 0, 0)));

                    b.Property<int>("DataTypeId")
                        .HasColumnType("integer");

                    b.Property<bool?>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<string>("DeletedBy")
                        .HasColumnType("text");

                    b.Property<DateTimeOffset?>("DeletedWhen")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsRelevantForReportingEnabled")
                        .HasColumnType("boolean");

                    b.Property<string>("Name")
                        .HasMaxLength(40)
                        .HasColumnType("character varying(40)");

                    b.Property<int>("PanelId")
                        .HasColumnType("integer");

                    b.Property<int>("PeriodicityId")
                        .HasColumnType("integer");

                    b.Property<int>("PurposeId")
                        .HasColumnType("integer");

                    b.Property<int>("TypeId")
                        .HasColumnType("integer");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<DateTimeOffset?>("UpdatedWhen")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("DataTypeId");

                    b.HasIndex("PurposeId");

                    b.ToTable("BaseProject");
                });

            modelBuilder.Entity("DWH.ProjectServices.API.Infrastructure.Persistence.Entities.BaseProjectPanelEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Name")
                        .HasMaxLength(40)
                        .HasColumnType("character varying(40)");

                    b.HasKey("Id");

                    b.ToTable("BaseProject_Panel");
                });

            modelBuilder.Entity("DWH.ProjectServices.API.Infrastructure.Persistence.Entities.BaseProjectPredecessorEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("BaseProjectId")
                        .HasColumnType("integer");

                    b.Property<int>("PredecessorId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("BaseProjectId");

                    b.ToTable("BaseProject_Predecessor");
                });

            modelBuilder.Entity("DWH.ProjectServices.API.Infrastructure.Persistence.Entities.BaseProjectProductGroupEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("BaseProjectId")
                        .HasColumnType("integer")
                        .HasColumnName("BaseProjectId");

                    b.Property<bool?>("Deleted")
                        .HasColumnType("boolean");

                    b.Property<int>("ProductGroupId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("BaseProjectId");

                    b.ToTable("BaseProject_ProductGroup");
                });

            modelBuilder.Entity("DWH.ProjectServices.API.Infrastructure.Persistence.Entities.BaseProjectPurpose", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Name")
                        .HasMaxLength(40)
                        .HasColumnType("character varying(40)");

                    b.HasKey("Id");

                    b.ToTable("BaseProject_Purpose");
                });

            modelBuilder.Entity("DWH.ProjectServices.API.Infrastructure.Persistence.Entities.OutBoxItemEntity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Payload")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime?>("ProcessedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("varchar(20)");

                    b.Property<string>("TypeId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("OutBoxItem");
                });

            modelBuilder.Entity("DWH.ProjectServices.API.Infrastructure.Persistence.Entities.PeriodEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<long>("QCPeriodId")
                        .HasColumnType("bigint");

                    b.Property<long?>("RefPeriodId")
                        .HasColumnType("bigint");

                    b.Property<int?>("RefProjectId")
                        .HasColumnType("integer");

                    b.Property<int>("index")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("QCPeriodId");

                    b.ToTable("Period");
                });

            modelBuilder.Entity("DWH.ProjectServices.API.Infrastructure.Persistence.Entities.QCPeriodEntity", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<long>("Id"));

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTimeOffset>("CreatedWhen")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("timestamp with time zone")
                        .HasDefaultValue(new DateTimeOffset(new DateTime(2025, 5, 16, 15, 23, 28, 867, DateTimeKind.Unspecified).AddTicks(5084), new TimeSpan(0, 0, 0, 0, 0)));

                    b.Property<long>("PeriodId")
                        .HasColumnType("bigint");

                    b.Property<int>("QCProjectId")
                        .HasColumnType("integer");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<DateTimeOffset?>("UpdatedWhen")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("QCProjectId");

                    b.ToTable("QCPeriod");
                });

            modelBuilder.Entity("DWH.ProjectServices.API.Infrastructure.Persistence.Entities.QCProjectEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("BaseProjectId")
                        .HasColumnType("integer");

                    b.Property<bool?>("IsAutoLoad")
                        .HasColumnType("boolean");

                    b.Property<bool?>("IsAutomatedPriceCheck")
                        .HasColumnType("boolean");

                    b.Property<int?>("ResetCorrectionTypeId")
                        .HasColumnType("integer");

                    b.Property<int?>("SQCMode")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("BaseProjectId")
                        .IsUnique();

                    b.HasIndex("ResetCorrectionTypeId");

                    b.ToTable("QCProject");
                });

            modelBuilder.Entity("DWH.ProjectServices.API.Infrastructure.Persistence.Entities.RequestStatusEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.HasKey("Id");

                    b.ToTable("RequestStatus");
                });

            modelBuilder.Entity("DWH.ProjectServices.API.Infrastructure.Persistence.Entities.RetailerSeperationEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<bool>("IsError")
                        .HasColumnType("boolean");

                    b.Property<int>("RetailerBPId")
                        .HasColumnType("integer");

                    b.Property<int>("RetailerSeperationRequestId")
                        .HasColumnType("integer");

                    b.Property<int>("SourceBPId")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("RetailerSeperationRequestId");

                    b.ToTable("RetailerSeperation");
                });

            modelBuilder.Entity("DWH.ProjectServices.API.Infrastructure.Persistence.Entities.RetailerSeperationRequestDetailEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<int>("RequestStatusId")
                        .HasColumnType("integer");

                    b.Property<int>("RetailerSeperationRequestId")
                        .HasColumnType("integer");

                    b.Property<string>("UpdatedBy")
                        .HasColumnType("text");

                    b.Property<DateTimeOffset>("UpdatedWhen")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("RetailerSeperationRequestId");

                    b.ToTable("RetailerSeperationRequestDetail");
                });

            modelBuilder.Entity("DWH.ProjectServices.API.Infrastructure.Persistence.Entities.RetailerSeperationRequestEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<bool>("Extrapolation")
                        .HasColumnType("boolean");

                    b.Property<long>("FromPeriodId")
                        .HasColumnType("bigint");

                    b.Property<string>("JiraId")
                        .HasColumnType("text");

                    b.Property<int>("RequestStatusId")
                        .HasColumnType("integer");

                    b.Property<bool>("ResetCorrection")
                        .HasColumnType("boolean");

                    b.Property<long>("ToPeriodId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.ToTable("RetailerSeperationRequest");
                });

            modelBuilder.Entity("DWH.ProjectServices.API.Infrastructure.Persistence.Entities.StockInitializationEntity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<long>("QCPeriodId")
                        .HasColumnType("bigint");

                    b.Property<int?>("StockBaseProjectId")
                        .HasColumnType("integer");

                    b.Property<long?>("StockPeriodId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("QCPeriodId")
                        .IsUnique();

                    b.ToTable("StockInitialization");
                });

            modelBuilder.Entity("DWH.ProjectServices.API.Models.ResetCorrectionType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("Name")
                        .HasMaxLength(40)
                        .HasColumnType("character varying(40)");

                    b.HasKey("Id");

                    b.ToTable("ResetCorrectionTypes");
                });

            modelBuilder.Entity("DWH.ProjectServices.API.Infrastructure.Persistence.Entities.BaseProjectEntity", b =>
                {
                    b.HasOne("DWH.ProjectServices.API.Infrastructure.Persistence.Entities.BaseProjectDataType", "DataType")
                        .WithMany()
                        .HasForeignKey("DataTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("DWH.ProjectServices.API.Infrastructure.Persistence.Entities.BaseProjectPurpose", "Purpose")
                        .WithMany()
                        .HasForeignKey("PurposeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DataType");

                    b.Navigation("Purpose");
                });

            modelBuilder.Entity("DWH.ProjectServices.API.Infrastructure.Persistence.Entities.BaseProjectPredecessorEntity", b =>
                {
                    b.HasOne("DWH.ProjectServices.API.Infrastructure.Persistence.Entities.BaseProjectEntity", null)
                        .WithMany("Predecessors")
                        .HasForeignKey("BaseProjectId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_BaseProject_Predecessors_BaseProject_BaseProjectId");
                });

            modelBuilder.Entity("DWH.ProjectServices.API.Infrastructure.Persistence.Entities.BaseProjectProductGroupEntity", b =>
                {
                    b.HasOne("DWH.ProjectServices.API.Infrastructure.Persistence.Entities.BaseProjectEntity", null)
                        .WithMany("ProductGroups")
                        .HasForeignKey("BaseProjectId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_BaseProject_ProductGroups_BaseProject_BaseProjectId");
                });

            modelBuilder.Entity("DWH.ProjectServices.API.Infrastructure.Persistence.Entities.PeriodEntity", b =>
                {
                    b.HasOne("DWH.ProjectServices.API.Infrastructure.Persistence.Entities.QCPeriodEntity", null)
                        .WithMany("Periods")
                        .HasForeignKey("QCPeriodId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_Period_QCPeriod_QCPeriodId");
                });

            modelBuilder.Entity("DWH.ProjectServices.API.Infrastructure.Persistence.Entities.QCPeriodEntity", b =>
                {
                    b.HasOne("DWH.ProjectServices.API.Infrastructure.Persistence.Entities.QCProjectEntity", null)
                        .WithMany("QCPeriods")
                        .HasForeignKey("QCProjectId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_QCPeriod_QCProject_QCProjectId");
                });

            modelBuilder.Entity("DWH.ProjectServices.API.Infrastructure.Persistence.Entities.QCProjectEntity", b =>
                {
                    b.HasOne("DWH.ProjectServices.API.Infrastructure.Persistence.Entities.BaseProjectEntity", null)
                        .WithOne("QCProjects")
                        .HasForeignKey("DWH.ProjectServices.API.Infrastructure.Persistence.Entities.QCProjectEntity", "BaseProjectId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_BaseProject_QCProjects_BaseProjectId");

                    b.HasOne("DWH.ProjectServices.API.Models.ResetCorrectionType", null)
                        .WithOne("QCProject")
                        .HasForeignKey("DWH.ProjectServices.API.Infrastructure.Persistence.Entities.QCProjectEntity", "ResetCorrectionTypeId");
                });

            modelBuilder.Entity("DWH.ProjectServices.API.Infrastructure.Persistence.Entities.RetailerSeperationEntity", b =>
                {
                    b.HasOne("DWH.ProjectServices.API.Infrastructure.Persistence.Entities.RetailerSeperationRequestEntity", null)
                        .WithMany("RetailerSeperations")
                        .HasForeignKey("RetailerSeperationRequestId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_RetailerSeperation_RetailerSeperationRequests_RetailerSeper~");
                });

            modelBuilder.Entity("DWH.ProjectServices.API.Infrastructure.Persistence.Entities.RetailerSeperationRequestDetailEntity", b =>
                {
                    b.HasOne("DWH.ProjectServices.API.Infrastructure.Persistence.Entities.RetailerSeperationRequestEntity", null)
                        .WithMany("RetailerSeperationRequestDetails")
                        .HasForeignKey("RetailerSeperationRequestId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_RetailerSeperationRequestDetail_RetailerSeperationRequests_~");
                });

            modelBuilder.Entity("DWH.ProjectServices.API.Infrastructure.Persistence.Entities.StockInitializationEntity", b =>
                {
                    b.HasOne("DWH.ProjectServices.API.Infrastructure.Persistence.Entities.QCPeriodEntity", null)
                        .WithOne("StockInitialization")
                        .HasForeignKey("DWH.ProjectServices.API.Infrastructure.Persistence.Entities.StockInitializationEntity", "QCPeriodId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired()
                        .HasConstraintName("FK_StockInitialization_QCPeriod_QCPeriodId");
                });

            modelBuilder.Entity("DWH.ProjectServices.API.Infrastructure.Persistence.Entities.BaseProjectEntity", b =>
                {
                    b.Navigation("Predecessors");

                    b.Navigation("ProductGroups");

                    b.Navigation("QCProjects");
                });

            modelBuilder.Entity("DWH.ProjectServices.API.Infrastructure.Persistence.Entities.QCPeriodEntity", b =>
                {
                    b.Navigation("Periods");

                    b.Navigation("StockInitialization");
                });

            modelBuilder.Entity("DWH.ProjectServices.API.Infrastructure.Persistence.Entities.QCProjectEntity", b =>
                {
                    b.Navigation("QCPeriods");
                });

            modelBuilder.Entity("DWH.ProjectServices.API.Infrastructure.Persistence.Entities.RetailerSeperationRequestEntity", b =>
                {
                    b.Navigation("RetailerSeperationRequestDetails");

                    b.Navigation("RetailerSeperations");
                });

            modelBuilder.Entity("DWH.ProjectServices.API.Models.ResetCorrectionType", b =>
                {
                    b.Navigation("QCProject");
                });
#pragma warning restore 612, 618
        }
    }
}
