﻿namespace DWH.ProjectServices.API.Models
{
    public class ProductGroup
    {
        public ProductGroup(int id, string description, int countryId, long sectorId, long categoryId, int panelId, int domainProductGroupId)
        {
            this.Id = id;
            Description = description;
            CountryId = countryId;
            SectorId = sectorId;
            CategoryId = categoryId;
            PanelId = panelId;
            DomainProductGroupId = domainProductGroupId;
        }

        public int Id { get; }
        public string Description { get; }
        public int CountryId { get; }
        public long CategoryId { get; }
        public long SectorId { get; }
        public int PanelId { get; }
        public int HybridFlag { get; }
        public int Deleted { get; }
        public int DomainProductGroupId {get;}

    }
}
