﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request
{
    public class BaseProjectDeleteRequest : IValidatableObject
    {
        [Required]
        public List<int> Ids { get; set; }

        public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
        {
            var results = new List<ValidationResult>();
            if (Ids.Count <= 0)
            {
                results.Add(new ValidationResult("Must have atleast one id to proceed"));
            }

            return results;
        }

        [JsonIgnore]
        public string DeletedBy { get; set; }

        [JsonIgnore]
        public string UserName { get; set; }
    }
}
