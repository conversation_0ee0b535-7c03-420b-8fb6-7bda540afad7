﻿using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request
{
    public class BaseProjectEditRequest : IValidatableObject
    {
        [MaxLength(40)]
        public string Name { get; set; }
        public int DataTypeId { get; set; }
        public int PurposeId { get; set; }
        public bool IsRelevantForReportingEnabled { get; set; }
        public ICollection<BaseProjectPredecessorModelDto> Predecessors { get; set; }
        public ICollection<BaseProjectProductGroupModelDto> ProductGroups { get; set; }
        public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
        {
            var results = new List<ValidationResult>();

            if (string.IsNullOrEmpty(Name))
            {
                results.Add(new ValidationResult("Base Project Name cannot be null or empty"));
            }
            return results;
        }

        [JsonIgnore]
        public string UpdatedBy { get; set; }
        public bool Deleted { get; set; }

    }
}
