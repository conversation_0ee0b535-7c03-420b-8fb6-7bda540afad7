﻿using DWH.ProjectServices.API.Domain.Models;

namespace DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;

public class QCPeriodModelDto
{
    public long Id { get; set; }

    public int QCProjectId { get; set; }
    public long PeriodId { get; set; }
    public ICollection<Period> Periods { get; set; }
    public StockInitialization StockInitialization { get; set; }
    public DateTimeOffset DateOfCreation { get; set; }
    public DateTimeOffset? UpdatedWhen { get; set; }
    public string CreatedBy { get; set; }

    public string UpdatedBy { get; set; }
}

public class QCPeriodWithBPIdResponse : QCPeriodModelDto
{
    public int BaseProjectId { get; set; }
}
