﻿using System.Text.Json.Serialization;

namespace DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request
{
    public class QCProjectEditRequest
    {
        public int? ResetCorrectionTypeId { get; set; }

        public bool? IsAutoLoad { get; set; }

        public int? SQCMode { get; set; }

        public bool? IsAutomatedPriceCheck { get; set; }
        [JsonIgnore]
        public string UpdatedBy { get; set; }

    }
}
