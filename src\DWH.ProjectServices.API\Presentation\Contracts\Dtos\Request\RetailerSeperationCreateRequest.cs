﻿using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request
{
    public class RetailerSeperationCreateRequest
    {
        public long FromPeriodId { get; set; }
        public long ToPeriodId { get; set; }

        public bool ResetCorrection { get; set; }
        public bool Extrapolation { get; set; }
        public ICollection<RetailerSeperationModelDto> RetailerSeperations { get; set; }

        [JsonIgnore]
        public ICollection<RetailerSeperationRequestDetailModelDto> RetailerSeperationRequestDetails { get; set; }

        [JsonIgnore]
        public int RequestStatusId { get; set; }
    }
}
