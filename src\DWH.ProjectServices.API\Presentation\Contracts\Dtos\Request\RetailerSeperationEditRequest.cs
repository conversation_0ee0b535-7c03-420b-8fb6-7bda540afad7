﻿using System.Text.Json.Serialization;

namespace DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request
{
    public class RetailerSeperationEditRequest
    {
        public long FromPeriodId { get; set; }
        public long ToPeriodId { get; set; }
        public bool ResetCorrection { get; set; }
        public bool Extrapolation { get; set; }
        public ICollection<RetailerSeperationEditModelDto> RetailerSeperations { get; set; }
    }
}
