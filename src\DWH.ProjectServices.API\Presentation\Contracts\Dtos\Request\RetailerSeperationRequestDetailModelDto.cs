﻿using System.Text.Json.Serialization;

namespace DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request
{
    public class RetailerSeperationRequestDetailModelDto
    {
        [JsonIgnore]
        public int RequestStatusId { get; set; }
        [JsonIgnore]
        public string UpdatedBy { get; set; }

        public int RetailerSeperationRequestId { get; set; }

        public DateTimeOffset UpdatedWhen { get; set; }

    }
}
