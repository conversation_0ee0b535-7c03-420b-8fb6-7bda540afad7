﻿using System.ComponentModel.DataAnnotations;
using DWH.ProjectServices.API.Models.Dtos;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;

namespace DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response
{
    public class BaseProjectEditResponse
    {
        public int Id { get; set; }
        [MaxLength(40)]
        [Required]
        public string Name { get; set; }
        public int TypeId { get; set; }
        public int PanelId { get; set; }
        public int DataTypeId { get; set; }
        public int PurposeId { get; set; }
        public bool IsRelevantForReportingEnabled { get; set; }
        public int PeriodicityId { get; set; }
        public int CountryId { get; set; }

        public DateTimeOffset LastUpdateOn { get; set; }
        public int? LastUpdateBy { get; set; }

        public bool IsDeleted { get; set; } 


        public ICollection<BaseProjectPredecessorModelDto> Predecessors { get; set; }

        public ICollection<BaseProjectProductGroupModelDto> ProductGroups { get; set; }
    }
}
