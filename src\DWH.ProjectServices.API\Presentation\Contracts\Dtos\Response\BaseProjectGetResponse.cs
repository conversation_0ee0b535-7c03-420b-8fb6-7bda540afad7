﻿using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Models.Dtos;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;
using System.ComponentModel.DataAnnotations;

namespace DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response
{


    public class BaseProjectGetResponse
    {

        public int Id { get; set; }
        [MaxLength(40)]
        [Required]
        public string Name { get; set; }
        [Required]
        public int TypeId { get; set; }
        public int PanelId { get; set; }
        public bool IsRelevantForReportingEnabled { get; set; }
        public int DataTypeId { get; set; }
        public int PurposeId { get; set; }
        [Required]
        public int PeriodicityId { get; set; }
        [Required]
        public int CountryId { get; set; }
        public DateTimeOffset? UpdatedWhen { get; set; }
        public ICollection<BaseProjectPredecessorModelDto> Predecessors { get; set; }

        [Required]
        public ICollection<BaseProjectProductGroupModelDto> ProductGroups { get; set; }

        public QCProject QCProjects { get; set; }
        public DateTimeOffset DateOfCreation { get; set; }
        public string CreatedBy { get; set; }
        public string UpdatedBy { get; set; }
        public bool IsRetailerSeparationRequested { get; set; }
        public bool Deleted { get; set; }



    }
}
