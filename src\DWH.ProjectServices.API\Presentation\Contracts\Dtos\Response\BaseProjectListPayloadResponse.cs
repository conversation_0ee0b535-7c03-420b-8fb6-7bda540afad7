﻿using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;

namespace DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response
{
    public class BaseProjectListPayloadResponse
    {
            public int Id { get; set; }
            [MaxLength(40)]
            [Required]
            public string Name { get; set; }
            [Required]
            public int TypeId { get; set; }

            public int PanelId { get; set; }
            public int DataTypeId { get; set; }
            public int PurposeId { get; set; }
            [Required]
            public int PeriodicityId { get; set; }
            [Required]
            public int CountryId { get; set; }

            public int ProductGroupCount { get; set; }
        
            [JsonIgnore]
            public QCProject QCProjects { get; set; }

            public int? QcprojectId => QCProjects?.Id;

            public DataTypes DataType { get; set; }
            public Purposes Purpose { get; set; }
            public string UpdatedBy { get; set; }
            public DateTimeOffset? UpdatedWhen { get; set; }
        public bool Deleted { get; set; }


    }

}

