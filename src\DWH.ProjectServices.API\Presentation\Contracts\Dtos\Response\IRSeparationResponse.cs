﻿

using DWH.ProjectServices.API.Domain.Models;

namespace DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response
{
    
public class IRSeparationRecords
{
    public List<IRSeparationResponse> IRSeparationRecord { get; set; }
}
public class IRSeparationResponse
    {
        public int RetailerSeperationRequestId { get; set; }

    }

public class IRSeparationResponseBaseProject
{
    public int IndustryProject { get; set; }
    public int RetailerProject { get; set; }
    public int RetailerSeperationRequestId { get; set; }

}

}
