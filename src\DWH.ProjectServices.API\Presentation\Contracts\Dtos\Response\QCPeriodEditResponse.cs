﻿using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Infrastructure.Persistence.Entities;

namespace DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;

public class QCPeriodEditResponse
{

    public int QCProjectId { get; set; }
    public ICollection<Period> Periods { get; set; }
    public StockInitialization StockInitialization { get; set; }
    public long PeriodId { get; set; }

}
