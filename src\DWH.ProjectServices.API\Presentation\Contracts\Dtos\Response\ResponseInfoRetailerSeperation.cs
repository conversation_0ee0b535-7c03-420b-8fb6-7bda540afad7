﻿namespace DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response
{
    public class ResponseInfoRetailerSeperation
    {
        public ResponseInfoRetailerSeperation(string sourceBpId, int statusCode, string statusMsg, int? requestId)
        {
            RequestId = (int)requestId;
            SourceBpId = sourceBpId;
            StatusCode = statusCode;
            StatusMsg = statusMsg;
        }
        public string SourceBpId { get; set; }

        public int StatusCode { get; set; }
        public string StatusMsg { get; set; }
        public int? RequestId { get; set; }
    }
}
