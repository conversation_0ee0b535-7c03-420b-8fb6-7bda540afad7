﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Linq;

namespace DWH.ProjectServices.API.Presentation.Controllers
{
    /// <summary>
    /// Base API controller
    /// Any API Controller should be better to be inherited from this controller
    /// </summary>

    [Authorize]
    [ApiController]
    [Route("api/v1/[controller]")]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(typeof(void), StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
    public abstract class ApiController : ControllerBase
    {
        internal IActionResult OkOrEmpty<T>(T value) => value == null ? NotFound() : Ok(value);
        internal IActionResult OkOrEmptyList<T>(IEnumerable<T> value) => value == null || !value.Any() ? NotFound() : Ok(value);
    }
}
