﻿using BootstrapAPI.Core.Controllers;
using DWH.ProjectServices.API.Models;

using DWH.ProjectServices.API.Services.Interfaces;
using DWH.ProjectServices.API.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Net;
using Microsoft.AspNetCore.DataProtection.KeyManagement;
using System.Data.Common;
using AutoMapper;
using Microsoft.AspNetCore.Http.HttpResults;
using DWH.ProjectServices.API.Models.Dtos;
using DWH.ProjectServices.API.Infrastructure.Persistence.Entities;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;

namespace DWH.ProjectServices.API.Presentation.Controllers
{
    public class ProjectSubTypesController : ApiController
    {
        private readonly IMapper _mapper; 
        private readonly IProjectSubTypeService _projectSubTypeService;

        public ProjectSubTypesController(IProjectSubTypeService projectSubTypeService, IMapper mapper)
        {
            _projectSubTypeService = projectSubTypeService;
            _mapper = mapper;
        }


        /// <summary>
        /// Gets all ProjectSubtypes
        /// </summary>
        [HttpGet]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(IReadOnlyCollection<ProjectSubTypeResponse>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status204NoContent)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetAllAsync()
        {
            //var projecSubTypes = await _projectSubTypeService.GetAllAsync();
            //var result = _mapper.Map<IReadOnlyCollection<ProjectSubTypeResponse>>(projecSubTypes);
            //return OkOrEmptyList(result);

            var projecSubTypes = await _projectSubTypeService.GetAllAsync();
            var result = projecSubTypes?.Select(p => new ProjectSubTypeResponse(p.Id, p.Name)).ToList();


            return OkOrEmptyList(result);
        }




    }
}
