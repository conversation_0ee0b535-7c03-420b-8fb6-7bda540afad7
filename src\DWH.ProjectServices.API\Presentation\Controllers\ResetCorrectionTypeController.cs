﻿using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Infrastructure.Persistence.Entities;
using DWH.ProjectServices.API.Models;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;
using DWH.ProjectServices.API.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using AutoMapper;

namespace DWH.ProjectServices.API.Presentation.Controllers
{
    public class ResetCorrectionTypeController : ApiController
    {
        private readonly IResetCorrectionTypeService _resetCorrectionTypeService;
        private readonly IMapper _mapper;

        public ResetCorrectionTypeController(IResetCorrectionTypeService ResetCorrectionTypeService, IMapper mapper)
        {
            _resetCorrectionTypeService = ResetCorrectionTypeService;
            _mapper = mapper;
        }

        [HttpGet]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(typeof(IEnumerable<ResetCorrectionTypeResponse>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status204NoContent)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status404NotFound)]
        [ProducesResponseType(typeof(ProblemDetails), StatusCodes.Status500InternalServerError)]
        public async Task<IActionResult> GetAllAsync()
        {
            var resetCorrectionTypes = await _resetCorrectionTypeService.GetAllAsync();
            if (resetCorrectionTypes == null) 
            {
                return NotFound();
            }
            var result = _mapper.Map<IReadOnlyCollection<ResetCorrectionTypeResponse>> (resetCorrectionTypes);

            return OkOrEmpty(result);
        }
    }
}

