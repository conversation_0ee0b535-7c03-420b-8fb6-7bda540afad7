﻿using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Infrastructure.WebServiceClient.Interfaces;
using DWH.ProjectServices.API.Services.Constants;
using DWH.ProjectServices.API.Services.Helper.Interface;
using Newtonsoft.Json;
using System.Text;
using System.Xml.Linq;

namespace DWH.ProjectServices.API.Services.Helper
{
    public class JiraHelper : IJiraHelper
    {
        private const string API_ENDPOINT = "/api/v1/Ticket/RetailerSeparation";
        private readonly IJiraApiClient _jiraApiClient;

        public JiraHelper(IJiraApiClient jiraApiClient)
        {
            _jiraApiClient = jiraApiClient;
        }
        
        public async Task<JiraTicket> PostTicket(CreateTicketDetails createTicketDetails)
        {
            var requestContent = new StringContent(JsonConvert.SerializeObject(createTicketDetails), Encoding.UTF8, "application/json");

            var response = await _jiraApiClient.PostAsync<JiraTicket>(API_ENDPOINT, requestContent);
            if (!response.IsSuccess)
            {
                throw new HttpRequestException($"Error calling API for current Jira: Status Code {(int)response.StatusCode} ({response.ReasonPhrase})");
            }
            return response.Data;
        }

        public async Task<JiraTicket> PutTicket(TicketDetails ticketDetails)
        {
            var requestContent = new StringContent(JsonConvert.SerializeObject(ticketDetails), Encoding.UTF8, "application/json");

            var response = await _jiraApiClient.PutAsync<JiraTicket>(API_ENDPOINT, requestContent);

            if (!response.IsSuccess)
            {
                throw new HttpRequestException($"Error calling API for current Jira: Status Code {(int)response.StatusCode} ({response.ReasonPhrase})");
            }
            return response.Data;
        }

        public string GenerateTicketComments(CommentDetails commentDetails)
        {
            string comments = string.Empty;
            switch (commentDetails.TicketStatus)
            {
                case Domain.Enum.TicketStatus.Message:
                    comments = GetUpdateComments(commentDetails.UserEmail, (RetailerSeperationRequest)commentDetails.TicketDetails, commentDetails.ErrorDetails);
                    break;
                case Domain.Enum.TicketStatus.Resolved:
                    comments = GetResolveComments(commentDetails.UserEmail, (int)commentDetails.TicketDetails, commentDetails.ErrorDetails);
                    break;
            }
            return comments;
        }

        private string GetUpdateComments(string userEmail, RetailerSeperationRequest retailerseparationEditRequest, string errorDetails)
        {
            string comments;
            bool isErrorCase = retailerseparationEditRequest.ToPeriodId == -1;

            if (isErrorCase)
            {
                var isError = retailerseparationEditRequest.RetailerSeperations.FirstOrDefault()?.IsError ?? false;
                var sourcebpId = retailerseparationEditRequest.RetailerSeperations.FirstOrDefault()?.SourceBPId ?? 0;

                if (isError)
                {
                    comments = $"{userEmail} has updated that the Source BP ID {sourcebpId} has got an error. The reason is: {errorDetails}";
                }
                else
                {
                    comments = $"{userEmail} has updated that the error is removed from Source BP ID {sourcebpId}";
                }
            }
            else
            {
                bool isEditAll = retailerseparationEditRequest.RetailerSeperations.Select(r => r.SourceBPId).Count() > 1;
                if (isEditAll)
                {
                    comments = $"{userEmail} has made some changes affecting the attributes of all Source Base Projects.";
                }
                else
                {
                    comments = $"{userEmail} has made some changes in the attributes of Source BP ID {retailerseparationEditRequest.RetailerSeperations.SingleOrDefault().SourceBPId}.";
                }
            }
            return comments;
        }

        private string GetResolveComments(string userEmail, int statusId, string errorDetails)
        {
            string comments;
            if (statusId == RetailerRequestConstants.Declined)
            {
                comments = $"{userEmail} has declined the Jira ticket due to this reason: {errorDetails}";
            }
            else
            {
                comments = "Retailer Separation job is completed, and the ticket is Resolved.";
            }
            return comments;
        }
    }
}
