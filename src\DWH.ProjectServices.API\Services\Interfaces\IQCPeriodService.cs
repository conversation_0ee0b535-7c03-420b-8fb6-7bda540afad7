﻿using DWH.ProjectServices.API.Models.Dtos;
using DWH.ProjectServices.API.Models;
using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;

namespace DWH.ProjectServices.API.Services.Interfaces
{
    public interface IQCPeriodService
    {
        Task<QCPeriod> AddAsyncQCPeriod(QCPeriod qcPeriod);
        Task<QCPeriod> EditPeriodAsync(long QCPeriodId, QCPeriodEdits qcPeriodEdit);
        Task<IEnumerable<QCPeriod>> GetAllQCPeriodsAsync(int qcProjectId);
        Task<QCPeriodWithBPIdResponse> GetQCPeriodAsync(long qcPeriodId);
        Task<IReadOnlyList<ResponseInfoQCPeriod>> DeletePeriodAsync(QCPeriodDeletes qcPeriodDelete);
        Task<AutoQCResponses> AutoQCPeriodCreateAsync(long targetPeriodId, AutoQCPeriods autoQCPeriodRequest,string userName);

        Task<BulkQCResponses> CreateBulkQCPeriodAsync(int qcProjectId, BulkQCPeriods bulkQCPeriodRange, string userName);

        Task<List<long>> GetAuthorizedCountriesForPeriods(QCPeriodCountries qcPeriodCountryRequest);

    }
}
