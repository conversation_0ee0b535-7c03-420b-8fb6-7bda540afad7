﻿using Npgsql;
using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;

namespace DWH.ProjectServices.API.Services.Interfaces
{
    public interface IQCProjectService
    {

        Task<QCProject> UpdateAsync(int qcProjectId, QCProjectUpdates qcProject);
        Task<List<int>> GetAsync(QCProjectCountries qcProjectCountryRequest);

        Task<int> GetFilteredQCProjectAsync(QCProjectCountryIds qcProjectCountryRequest);

    }
}
