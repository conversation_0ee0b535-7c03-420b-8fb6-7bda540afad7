﻿using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;

namespace DWH.ProjectServices.API.Services.Interfaces
{
    public interface IRetailerSeperationService
    {
        Task<RetailerSeperationRequest> AddAsync(RetailerSeperationRequest retailerSeperation, string email);
        Task<RetailerSeperationRequest> GetAsync(int requestId);
        Task<RetailerSeperationRequest> UpdateAsync(string userName, string userEmail, RetailerSeperationRequest rsEditRequest, string errorDetails);
        Task<IReadOnlyList<int>> GetAuthorizedBaseProjectsByCountry(RetailerSeparationCountries rsCountryRequest);
        Task<RetailerSeperationLists> GetAsyncList(RetailerSeperationsLists retailerSeperationLists);
        Task<IReadOnlyList<string>> GetUsersList();
        Task<List<int>> GetSourceBaseProjects(int retailerSeperationRequestId);
        Task AddRetailerBPIdAsync(int sourceBPId, int retailerSeperationRequestId, int newRetailerBPId);
        Task AddDetailAndUpdateStatusAsync(RetailerSeperationRequestDetail details);
        Task<bool> UpdateStatusAsync(RetailerSeparationStatusDetails rsStatusDetails);
        Task<List<ResponseInfoRetailerSeperation>> DeleteSourceBPAsync(IRSeperationDeletes IRSeperationDeletes);
        Task<IReadOnlyList<int>> GetAuthorizedRetailerSeperationsByCountry(RetailerSeparationCountries rsCountryRequest);
        Task<int> PerformRetailerSeparationAsync(int baseProjectId, int TypeId,string username, int retailerSeperationRequestId, int indexSourceBP, int TotalSourceBP);
        Task PerformRetailerSeparationAsyncToRabbitMQ(int baseProjectId, int TypeId, string username, int retailerSeperationRequestId,int indexSourceBP, int TotalSourceBP);
        Task UpdateJiraTicketAsync(int retailerSeperationRequestId, string ticketStatus = null, string comment = null, bool? restrictComment = null);
        Task UpdateRetailerSeparationRequestStatus(int retailerSeperationRequestId);
    }
}
