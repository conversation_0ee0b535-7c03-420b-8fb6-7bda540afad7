﻿using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ.Constants;
using DWH.ProjectServices.API.Models;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;
using DWH.ProjectServices.API.Services.Constants;
using DWH.ProjectServices.API.Services.Helper.Interface;
using DWH.ProjectServices.API.Services.Interfaces;
using RabbitMQ.Client.Events;
using System.Net.Http;
using System.Text;
using System.Text.Json;

namespace DWH.ProjectServices.API.Services
{
    public class MessageSync : IMessageSync
    {
        private readonly ILogger<MessageSync> _logger;
        IConfiguration _configuration;
        private readonly IProjectServicesHelper _projectServicesHelper;

        public MessageSync(ILogger<MessageSync> logger, IConfiguration configuration, IProjectServicesHelper projectServicesHelper)
        {
            _logger = logger;
            _configuration = configuration;
            _projectServicesHelper = projectServicesHelper;
        }

        public async Task PerformSync(BasicDeliverEventArgs ea)
        {
            try
            {
                if (ea == null)
                {
                    _logger.LogInformation("MessageSync - Event Arguments is NULL");
                }

                var body = ea.Body.ToArray();
                var message = Encoding.UTF8.GetString(body);
                _logger.LogInformation($">>> MESSAGE JSON :: {message}");

                var retailerSeparationsData = JsonSerializer.Deserialize<RetailerSeparationsData>(message);
                if (retailerSeparationsData == null)
                {
                   _logger.LogInformation("MessageSync - Deserialization failed or message is null");
                   return;
                }
                
                _logger.LogInformation($"Base Project Id: {retailerSeparationsData.Id} :: Retailer Request Id: {retailerSeparationsData.RetailerSeparationRequestId}");
                string syncIds = retailerSeparationsData.SyncingEntityId.ToString();
                if (string.IsNullOrEmpty(syncIds))
                {
                    _logger.LogInformation("MessageSync - Invalid message received. Sync Id(s) not found");
                }

                if (IsRetailerSeparationExchange(ea))
                {
                    var request = new IRSeparationBaseProjectRequest
                    {
                        SourceBaseProjectId = Convert.ToInt32(syncIds),
                        retailerSeparationId = retailerSeparationsData.RetailerSeparationRequestId,
                        indexSourceBP = retailerSeparationsData.IndexSourceBP,
                        totalSourceBP = retailerSeparationsData.TotalSourceBP
                    };
                    await _projectServicesHelper.PerformRetailerSeparation(request, retailerSeparationsData.username);
                    _logger.LogInformation("Retailer Separation executed Successfully");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "EXCEPTION - PerformSync");
            }
        }

        private bool IsRetailerSeparationExchange(BasicDeliverEventArgs ea)
        {
            bool isEnabled = _configuration.GetValue("ExchangeEntitySettings:RetailerSeparationExchangeEnabled", false);
            if (isEnabled)
            {
                if (ea.Exchange.Equals(RetailerSeparationConstants.RetailerSeparationExchange, StringComparison.OrdinalIgnoreCase) && ea.RoutingKey.Equals(RMQConstants.CreateRoutingKey, StringComparison.OrdinalIgnoreCase))
                {
                    return true;
                }
            }
            else
            {
                _logger.LogInformation($"ExchangeEntitySettings - RetailerSeparation is disabled");
            }
            return false;
        }
    }
}
