﻿using DWH.ProjectServices.API.Infrastructure.SignalR;
using DWH.ProjectServices.API.Services;
using DWH.ProjectServices.API.Services.Interfaces;
using Microsoft.AspNetCore.SignalR;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using static DWH.ProjectServices.API.Presentation.Controllers.NotificationController;

namespace DWH.ProjectServices.API.Services
{ 
    public class NotificationService : INotificationService
    {
        private readonly IHubContext<NotificationHub> _hubContext;
        private readonly IRetailerSeperationService _retailerSeparationService;

        public NotificationService(IHubContext<NotificationHub> hubContext, IRetailerSeperationService retailerSeparationService)
        {
            _hubContext = hubContext;
            _retailerSeparationService = retailerSeparationService;
        }

        public async Task SendNotificationToUserAsync(string username, NotificationRequest notificationRequest)
        {
            var connectionId = NotificationHub.GetConnectionId(username);

            if (!string.IsNullOrEmpty(connectionId))
            {
                await _hubContext.Clients.Client(connectionId).SendAsync("ReceiveNotification", notificationRequest.Message);
            }

            await _retailerSeparationService.UpdateRetailerSeparationRequestStatus(notificationRequest.RetailerSeparationRequestId);
        }
    }
}