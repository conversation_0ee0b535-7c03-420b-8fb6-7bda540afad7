﻿using AutoMapper;
using BootstrapAPI.Core.Exception.Instances;
using DWH.ProjectServices.API.Domain.Enum;
using DWH.ProjectServices.API.Domain.Models;
using DWH.ProjectServices.API.Infrastructure.Persistence.Repositories.Interfaces;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ.Constants;
using DWH.ProjectServices.API.Infrastructure.RabbitMQ.Interfaces;
using DWH.ProjectServices.API.Models;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Request;
using DWH.ProjectServices.API.Presentation.Contracts.Dtos.Response;
using DWH.ProjectServices.API.Services.Constants;
using DWH.ProjectServices.API.Services.Helper.Interface;
using DWH.ProjectServices.API.Services.Interfaces;

namespace DWH.ProjectServices.API.Services
{
    public class RetailerSeperationService : IRetailerSeperationService
    {
        private readonly IMapper _mapper;
        private readonly ILogger<RetailerSeperationService> _logger;
        private readonly IRetailerSeperationRepository _retailerSeperationRepository;
        private IJiraHelper _jiraHelper;
        private readonly IConfiguration _configuration;
        private readonly IBaseProjectRepository _baseProjectRepository;
        private readonly ISecurityHelper _securityHelper;
        private readonly IOutBoxItemRepository _outBoxItemRepository;

        public RetailerSeperationService(IMapper mapper, IRetailerSeperationRepository retailerSeperationRepository,
            ILogger<RetailerSeperationService> logger, IJiraHelper jiraHelper,IOutBoxItemRepository outBoxItemRepository , IConfiguration configuration, 
            IBaseProjectRepository baseProjectRepository, ISecurityHelper securityHelper)

        {
            _retailerSeperationRepository = retailerSeperationRepository;
            _mapper = mapper;
            _logger = logger;
            _jiraHelper = jiraHelper;
            _configuration = configuration;
            _baseProjectRepository = baseProjectRepository;
            _securityHelper = securityHelper;
            _outBoxItemRepository = outBoxItemRepository;
        }

        public async Task<RetailerSeperationRequest> AddAsync(RetailerSeperationRequest retailerSeperation, string email)
        {
            if (retailerSeperation.RetailerSeperations.Count > 20)
            {
                _logger.LogError("RetailerSeperationService - AddAsync EXCEPTION :: Cannot add more than 20 base projects in a single I/R request.");
                throw new InvalidOperationException("You cannot add more than 20 base projects in a single I/R request.");
            }

            //QC Status Check work
            var qcStatusResponse = await CheckQCStatus(retailerSeperation.RetailerSeperations.Select(x => x.SourceBPId).ToList());
            if (!qcStatusResponse.CanAdd)
            {
                _logger.LogError($"RetailerSeperationService - AddAsync :: {qcStatusResponse.ErrorMessage}");
                throw new InvalidOperationException(qcStatusResponse.ErrorMessage);
            }

            var origin = _configuration["WebServiceClient:BaseAddress:Origin"];
            var CreateTicketDetails = new CreateTicketDetails
            {
                SourcebpIds = retailerSeperation.RetailerSeperations.Select(sbp => sbp.SourceBPId).ToArray(),
                Email = email,
            };
            var jiraData = _jiraHelper.PostTicket(CreateTicketDetails);
            if (jiraData.Result == null)
            {
                _logger.LogError($"RetailerSeperationService - AddAsync EXCEPTION :: Jira Ticket Creation Failed: {jiraData.Result}");
                throw new BadHttpRequestException($"Jira Ticket Creation Failed. {jiraData.Result}");
            }
            retailerSeperation.JiraId = jiraData.Result.name;
            var result = await _retailerSeperationRepository.AddAsync(retailerSeperation);
            var TicketDetails = new TicketDetails
            {
                TicketId = retailerSeperation.JiraId,
                TicketStatus = JiraConstants.Message,
                Comments = "Link to open this ticket in BuilderX is: " + origin + "retailer-separation/list/" + result.Id,
                RestrictComment = false
            };
            await _jiraHelper.PutTicket(TicketDetails);
            return result;
        }

        private async Task<(bool CanAdd, string ErrorMessage)> CheckQCStatus(List<int> sourceBpIds)
        {
            QCStatuses qcStatuses = new QCStatuses
            {
                BaseProjectIds = sourceBpIds
            };
            var qcStatusBps = await _baseProjectRepository.GetBaseProjectWithQCStatus(qcStatuses);
            if (qcStatusBps != null && qcStatusBps.Any())
            {
                string qcErrorMessage = "Selected BPs have at least one QC Period in QC Status. (" + string.Join(", ", qcStatusBps.Select(x => x)) + ")";
                return (false, qcErrorMessage);
            }
            return (true, string.Empty);
        }
        public async Task<RetailerSeperationRequest> GetAsync(int requestId)
        {
            var result = await _retailerSeperationRepository.GetAsync(requestId);
            return result;
        }

        public async Task<IReadOnlyList<int>> GetAuthorizedBaseProjectsByCountry(RetailerSeparationCountries retailerseparationCountryRequest)
        {
            var result = await _retailerSeperationRepository.GetAuthorizedBaseProjectsByCountry(retailerseparationCountryRequest);
            return result;
        }

        public async Task<RetailerSeperationRequest> UpdateAsync(string username, string userEmail, RetailerSeperationRequest retailerseparationEditRequest, string errorDetails)
        {
            string jiraId = await _retailerSeperationRepository.GetJiraId(retailerseparationEditRequest.Id);

            string comments = _jiraHelper.GenerateTicketComments(
                new CommentDetails
                {
                    UserEmail = userEmail,
                    TicketDetails = retailerseparationEditRequest,
                    ErrorDetails = errorDetails,
                    TicketStatus = TicketStatus.Message
                });

            var ticketDetails = new TicketDetails
            {
                TicketId = jiraId,
                TicketStatus = JiraConstants.Message,
                Comments = comments,
                RestrictComment = true
            };

            await _jiraHelper.PutTicket(ticketDetails);

            var result = await _retailerSeperationRepository.UpdateAsync(username, retailerseparationEditRequest);
            return result;
        }

        public async Task<RetailerSeperationLists> GetAsyncList(RetailerSeperationsLists retailerSeperationLists)
        {
            var retailerSeperationListDto = new RetailerSeperationLists();
            var retailerSeperationList = await _retailerSeperationRepository.GetAsyncList(retailerSeperationLists);

            retailerSeperationListDto.Counts = retailerSeperationList.Count();
            retailerSeperationListDto.MoreRecordsAvailable = retailerSeperationList.Count() >= 1000;

            ICollection<RetailerSeperationResponse> retailerSeperationRecordDto =
                _mapper.Map<ICollection<RetailerSeperationResponse>>(retailerSeperationList);

            retailerSeperationListDto.Records = retailerSeperationRecordDto;
            return retailerSeperationListDto;
        }

        public async Task<IReadOnlyList<string>> GetUsersList()
        {
            var retailerSeperationUsersList = await _retailerSeperationRepository.GetUsersList();
            return retailerSeperationUsersList;
        }

        public async Task<List<int>> GetSourceBaseProjects(int retailerSeperationRequestId)
        {
            var result = await _retailerSeperationRepository.GetBaseProjectsbyRequestIdAsync(retailerSeperationRequestId);
            return result;
        }

        public async Task AddRetailerBPIdAsync(int sourceBPId, int retailerSeperationRequestId, int newRetailerBPId)
        {
            await _retailerSeperationRepository.AddRetailerBPIdAsync(sourceBPId, retailerSeperationRequestId, newRetailerBPId);
        }

        public async Task AddDetailAndUpdateStatusAsync(RetailerSeperationRequestDetail details)
        {
            await _retailerSeperationRepository.AddDetailAndUpdateStatusAsync(details);
        }

        public async Task<bool> UpdateStatusAsync(RetailerSeparationStatusDetails retailerseparationStatusDetails)
        {
            var retailerseparationRequests = await _retailerSeperationRepository.GetAsync(retailerseparationStatusDetails.RequestId);
            if (retailerseparationRequests.RequestStatusId == RetailerRequestConstants.Pending)
            { await UpdateJiraTicketAsync(retailerseparationRequests.Id, ticketStatus: "In Progress", "Ticket has been closed"); }
            string comments = _jiraHelper.GenerateTicketComments(
                new CommentDetails
                {
                    UserEmail = retailerseparationStatusDetails.UserEmail,
                    TicketDetails = retailerseparationStatusDetails.StatusId,
                    ErrorDetails = retailerseparationStatusDetails.Reason,
                    TicketStatus = TicketStatus.Resolved
                });

            var ticketDetails = new TicketDetails
            {
                TicketId = retailerseparationRequests.JiraId,
                TicketStatus = JiraConstants.Resolved,
                Comments = comments,
                RestrictComment = false
            };

            await _jiraHelper.PutTicket(ticketDetails);

            var result = await _retailerSeperationRepository.UpdateStatusAsync(retailerseparationStatusDetails);
            if (!result)
            {
                _logger.LogError("RetailerSeperationService - UpdateStatusAsync EXCEPTION :: Provided request id {requestId} not found.", retailerseparationStatusDetails.RequestId);
                throw new EntityNotExistsException($"No Retailer Separation Request exists with Request Id {retailerseparationStatusDetails.RequestId}");
            }
            return result;
        }
        public async Task<IReadOnlyList<int>> GetAuthorizedRetailerSeperationsByCountry(RetailerSeparationCountries rsCountryRequest)
        {
            var result = await _retailerSeperationRepository.GetAuthorizedRetailerSeperationsByCountry(rsCountryRequest);
            return result;
        }
        public async Task<List<ResponseInfoRetailerSeperation>> DeleteSourceBPAsync(IRSeperationDeletes IRSeperationDeletes)
        {
            var responses = new List<ResponseInfoRetailerSeperation>();
            var result = await _retailerSeperationRepository.DeleteSourceBPAsync(IRSeperationDeletes);

            if (!string.IsNullOrEmpty(IRSeperationDeletes.Message) && result[0].RequestId.HasValue)
            {
                var requestObject = await _retailerSeperationRepository.GetAsync(result[0].RequestId.Value);
                var TicketDetails = new TicketDetails
                {
                    TicketId = requestObject.JiraId,
                    TicketStatus = JiraConstants.Message,
                    Comments = IRSeperationDeletes.Email + " has deleted the Source BP ID: " + result[0].SourceBpId + " due to this reason: " + IRSeperationDeletes.Message,
                    RestrictComment = false
                };
                await _jiraHelper.PutTicket(TicketDetails);
            }
            responses.AddRange(result);
            return responses;
        }

        public async Task<int> PerformRetailerSeparationAsync(int baseProjectId, int TypeId, string username, int retailerSeperationRequestId, int indexSourceBP, int TotalSourceBP)
        {
            var retailerBP = await _retailerSeperationRepository.PerformRetailerSeparation(baseProjectId, TypeId, username, retailerSeperationRequestId);
            string jiraId = await _retailerSeperationRepository.GetJiraId(retailerSeperationRequestId);

            SendUpdatedBPToRabbitMQ(baseProjectId);
             SendRetailerBPToRabbitMQ(retailerBP);
             CopyQCSecurityUsersToRabbitMQ(baseProjectId, retailerBP, username,indexSourceBP,TotalSourceBP, jiraId, retailerSeperationRequestId);
             CopyBPSecurityUsersToRabbitMQ(baseProjectId, retailerBP, username,indexSourceBP,TotalSourceBP, jiraId, retailerSeperationRequestId);

            return retailerBP.Id;
          

        }
        public async Task PerformRetailerSeparationAsyncToRabbitMQ(int baseProjectId, int TypeId, string username, int retailerSeperationRequestId, int indexSourceBP, int TotalSourceBP)
        {
            SendSourceBPtoRabbitMQ(baseProjectId, TypeId, username, retailerSeperationRequestId,indexSourceBP,TotalSourceBP);

        }
        private async Task SendSourceBPtoRabbitMQ(int baseProjectId, int TypeId,string username,int retailerSeperationRequestId, int indexSourceBP, int TotalSourceBP)
        {

            await _outBoxItemRepository.SaveMessagesAsync(
            new RetailerSeparationsData { Id = Guid.NewGuid(), BaseProjectId = baseProjectId, TypeId=TypeId,
                RetailerSeparationRequestId=retailerSeperationRequestId, username= username, SyncingEntityId= baseProjectId 
                ,IndexSourceBP= indexSourceBP,TotalSourceBP= TotalSourceBP 
            },
            ProjectMessageType.RetailerSeparationRequest);
        }
        private async Task SendUpdatedBPToRabbitMQ(int baseProjectId)
        {

                await _outBoxItemRepository.SaveMessagesAsync(
                new ProjectServicesData { Id = Guid.NewGuid(), SyncingEntityId = baseProjectId },
                ProjectMessageType.BaseProjectUpdate);

                await _outBoxItemRepository.SaveMessagesAsync(
                new ProjectServicesData { Id = Guid.NewGuid(), SyncingEntityId = baseProjectId },
                ProjectMessageType.QCProjectUpdate);
        }

        private async Task SendRetailerBPToRabbitMQ(BaseProject result)
        {


            await _outBoxItemRepository.SaveMessagesAsync(
            new ProjectServicesData { Id = Guid.NewGuid(), SyncingEntityId = result.Id },
            ProjectMessageType.BaseProjectCreate);

            await _outBoxItemRepository.SaveMessagesAsync(
            new ProjectServicesData { Id = Guid.NewGuid(), SyncingEntityId = result.QCProjects.BaseProjectId },
            ProjectMessageType.QCProjectCreate);


            if (result.QCProjects?.QCPeriods != null && result.QCProjects.QCPeriods.Any())
            {
                foreach (var qcPeriod in result.QCProjects.QCPeriods)
                {
                    if (qcPeriod != null)
                    {
                        await _outBoxItemRepository.SaveMessagesAsync(
                            new ProjectServicesData
                            {
                                Id = Guid.NewGuid(),
                                SyncingEntityId = qcPeriod.Id
                            },
                            ProjectMessageType.QCPeriodCreate);
                    }
                }
            }

        }

        public async Task UpdateJiraTicketAsync(int retailerSeperationRequestId, string ticketStatus = null, string comment = null, bool? restrictComment = null)
        {
            var jiraId = await _retailerSeperationRepository.GetJiraId(retailerSeperationRequestId);

            var TicketDetails = new TicketDetails
            {
                TicketId = jiraId,
                TicketStatus = ticketStatus ?? "Message",
                Comments = comment ?? "",
                RestrictComment = restrictComment ?? true
            };

            await _jiraHelper.PutTicket(TicketDetails);
        }

        public async Task CopyQCSecurityUsersToRabbitMQ(int sourceBaseProjectId, BaseProject retailerBP, string userName, int indexSourceBP, int TotalSourceBP, string JiraId, int retailerSeperationRequestId)
        {
            var sourceQCProjectId = await _retailerSeperationRepository.GetQCProjectIdAsync(sourceBaseProjectId);

            await _outBoxItemRepository.SaveMessagesAsync(
             new RetailerSeparationsSecurityData
             {
                 Id = Guid.NewGuid(),
                 SourceProjectId = sourceQCProjectId,
                 TargetProjectId = retailerBP.QCProjects.Id,
                 Username = userName,
                 TypeId = ProjectServicesSecurityConstants.QCSecurity,
                 SyncingEntityId = retailerBP.Id,
                 IndexSourceBP = indexSourceBP,
                 TotalSourceBP = TotalSourceBP,
                 JiraId = JiraId,
                 RetailerSeparationRequestId= retailerSeperationRequestId
             },
             ProjectMessageType.RetailerSeparationSecurityRequest);
        }
        private async Task CopyBPSecurityUsersToRabbitMQ(int sourceBaseProjectId, BaseProject retailerBP, string userName, int indexSourceBP, int TotalSourceBP, string JiraId,int retailerSeperationRequestId)
        {
            await _outBoxItemRepository.SaveMessagesAsync(
            new RetailerSeparationsSecurityData
            {
                Id = Guid.NewGuid(),
                SourceProjectId = sourceBaseProjectId,
                TargetProjectId = retailerBP.Id,
                Username = userName,
                TypeId = ProjectServicesSecurityConstants.BPSecurity,
                SyncingEntityId = retailerBP.Id,
                IndexSourceBP = indexSourceBP,
                TotalSourceBP = TotalSourceBP,
                JiraId = JiraId,
                RetailerSeparationRequestId = retailerSeperationRequestId
            },
            ProjectMessageType.RetailerSeparationSecurityRequest);
        }
        public async Task UpdateRetailerSeparationRequestStatus(int retailerSeperationRequestId)
        {
            await _retailerSeperationRepository.UpdateRetailerSeparationRequestStatus(retailerSeperationRequestId);    
        }

    }
}
