openapi: 3.0.2
info:
  title: Project Services API
  description: |-
    This is a project Services API contracts based on the 
  version: 1.0.0
components:
  schemas: 
     BaseProject:
       type: object
       properties:
         
          name:
              type: string
          typeId:
              type: integer
          modeId:
              type: integer
          isRelevantForReporting:
              type: boolean
          periodicityId:
              type: integer
          isQcEnabled:
              type: boolean
          predecessorIds:
              type: array
              items:
                type: integer
          productGroupIds:
             type: array
             items:
                type: integer
     ProductGroups:
        type: object
        properties:
           ProductGroupId:
             type: integer
          
     ProductGroup:
           type: object
           properties:
             id:
               type: integer
             name:
               type: string
           
     ProjectSubType: 
            type: object
            properties:
               id:
                   type: integer
               name:
                  type: string
     PredecessorBaseProject: 
            type: object
            properties:
              id:
                   type: integer
              name:
                  type: string
     BaseProjectEditRequest:
            title: Root Type for BaseProjectEditRequest
            description: this is the request which is sent to edit a base project
            type: object
            properties:
                Name:
                    type: string
                isRelevantForReporting:
                    type: boolean
                isAPCChecksEnabled:
                    type: boolean
                isQcEnabled:
                    type: boolean
                predecessorIds:
                    type: array
                    items:
                        format: int32
                        type: integer
            example:
                name: string
                isRelevantForReporting: true
                isAPCChecksEnabled: true
                isQcEnabled: true
                predecessorIds:
                    type: array
                    items:
                        format: int32
                        type: integer
     BaseProjectEditResponse:
            title: Root Type for BaseProjectEditResponse
            description: the response that is sent back after successful editing of a base project resource
            type: object
            properties:
                id:
                    format: int32
                    type: integer
                name:
                    type: string
                typeId:
                    format: int32
                    type: integer
                projectSubType:
                    format: int32
                    type: integer
                isRelevantForReporting:
                    type: boolean
                periodicityId:
                    format: int32
                    type: integer
                isQcEnabled:
                    type: boolean
                predecessorIds:
                    type: array
                    items:
                        format: int32
                        type: integer
                productGroupIds:
                    type: array
                    items:
                        format: int32
                        type: integer
                lastUpdatedOn:
                    format: date-time
                    type: string
                lastUpdatedBy:
                    type: string
            example:
                id: 1234
                name: My cool base project
                typeId: 0
                projectSubType: 123
                isRelevantForReporting: true
                periodicityId: 0
                isQcEnabled: true
                predecessorIds:
                    - 0
                productGroupIds:
                    - 0
                lastUpdatedOn: '2023-08-16T07:44:22.564Z'
                lastUpdatedBy: string
                

     ProblemDetails:
            title: Root Type for ProblemDetails
            description: >-
                the ProblemDetails error object to report and tell about error, some message and some
                addtional properties
            type: object
            properties:
                type:
                    type: string
                title:
                    type: string
                status:
                    format: int32
                    type: integer
                detail:
                    type: string
                instance:
                    type: string
                additionalProp1:
                    type: string
                additionalProp2:
                    type: string
                additionalProp3:
                    type: string
            example:
                type: string
                title: string
                status: 0
                detail: string
                instance: string
                additionalProp1: string
                additionalProp2: string
                additionalProp3: string    
     BaseProjectList:
            title: Root Type for BaseProjectListResponse
            description: Get array of base Project objects
            type: object
            properties:
                id:
                    format: int32
                    type: integer
                name:
                    type: string
                type:
                    format: int32
                    type: integer
                subType:
                    format: int32
                    type: integer
                countryId:
                    format: int32
                    type: integer
                productGroupIds:
                    type: array
                    items:
                        type: integer
                predecessorIds:
                    type: array
                    items:
                        type: integer
                periodcityId:
                    format: int32
                    type: integer
                
            example:
                id: 0
                name: string
                type: 0
                subType: 0
                countryId: 0
                productGroupIds: []
                predecessorIds: []
                periodcityId: 0
                
     
     BaseProjectEditModel:
            title: Root Type for BaseProjectEditModel
            description: ''
            type: object
            properties:
                id:
                    format: int32
                    type: integer
                name:
                    type: string
                typeId:
                    format: int32
                    type: integer
                projectSubType:
                    format: int32
                    type: integer
                isRelevantForReporting:
                    type: boolean
                periodicityId:
                    format: int32
                    type: integer
                isQcEnabled:
                    type: boolean
                predecessorIds:
                    type: array
                    items:
                        format: int32
                        type: integer
                productGroupIds:
                    type: array
                    items:
                        format: int32
                        type: integer
               
            example:
                id: 1234
                name: My cool base project
                typeId: 0
                projectSubType: 123
                isRelevantForReporting: true
                periodicityId: 0
                isQcEnabled: true
                predecessorIds:
                    - 0
                productGroupIds:
                    - 0
                


paths: 
  /api/v1/BaseProjects:
    post:
      summary: Create a BaseProject
      responses:
        '200':
          description: Created
        '400':
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/ProblemDetails'
                description: 'client validation error, model is not valid, bad user input, typos, wrong data types'
        '401':
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/ProblemDetails'
                description: returned when the user is not authenticated
        '403':
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/ProblemDetails'
                description: 'when accessing this endpoint with unsupported http verb, e.g. GET or POST'
        '404':
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/ProblemDetails'
                description: |-
                    base project not found because provided countryId is non-existing 
                    the url path was not correct
        '500':
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/ProblemDetails'
                description: 'database error, any server-related error '
      requestBody:
        content: 
           application/json:
             schema:
               $ref: '#/components/schemas/BaseProject'
      
 
  /api/v1/ProductGroups/{countryId}:
    parameters:
     - schema:
         type: integer
       name: countryId
       in: path
       required: true
    get:
       summary: Get ProductGroups
       responses:
        '200':
          description: Successful
          content:
            application/json:
              schema:
                type: array
                items:
                       $ref: '#/components/schemas/ProductGroup'
        '400':
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/ProblemDetails'
                description: 'client validation error, model is not valid, bad user input, typos, wrong data types'
        '401':
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/ProblemDetails'
                description: returned when the user is not authenticated
        '403':
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/ProblemDetails'
                description: 'when accessing this endpoint with unsupported http verb, e.g. GET or POST'
        '404':
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/ProblemDetails'
                description: |-
                    base project not found because provided countryId is non-existing 
                    the url path was not correct
        '500':
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/ProblemDetails'
                description: 'database error, any server-related error '
  /api/v1/ProjectSubTypes:
     get:
       summary: Get ProductSubTypes
       responses:
        '200':
          description: Successful
          content:
            application/json:
              schema:
                type: array
                items:
                       $ref: '#/components/schemas/ProjectSubType'
      
        '401':
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/ProblemDetails'
                description: returned when the user is not authenticated
        '403':
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/ProblemDetails'
                description: 'when accessing this endpoint with unsupported http verb, e.g. GET or POST'
        '404':
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/ProblemDetails'
                description: |-
                    base project not found because provided countryId is non-existing 
                    the url path was not correct
        '500':
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/ProblemDetails'
                description: 'database error, any server-related error '
  /api/v1/BaseProjects/{countryId}:
      parameters:
       - schema:
          type: integer
         name: countryId
         in: path
         required: true
       
      get:
       summary: Get BaseProject Predecessors
       responses:
        '200':
          description: Successful
          content:
            application/json:
              schema:
                type: array
                items:
                       $ref: '#/components/schemas/PredecessorBaseProject'
        '400':
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/ProblemDetails'
                description: 'client validation error, model is not valid, bad user input, typos, wrong data types'
        '401':
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/ProblemDetails'
                description: returned when the user is not authenticated
        '403':
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/ProblemDetails'
                description: 'when accessing this endpoint with unsupported http verb, e.g. GET or POST'
        '404':
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/ProblemDetails'
                description: |-
                    base project not found because provided countryId is non-existing 
                    the url path was not correct
        '500':
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/ProblemDetails'
                description: 'database error, any server-related error '
       
  /api/v1/baseprojects/{baseProjectId}:
      put:
        requestBody:
          description: ''
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BaseProjectEditRequest'
        parameters:
          - name: baseProjectId
            in: path
            description: ''
            required: true
            style: simple
            schema:
              minimum: 1
              type: integer
              format: int64
        responses:
            '200':
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/BaseProjectEditResponse'
                description: The base project was edited and persisted successfully
            '400':
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/ProblemDetails'
                description: 'client validation error, model is not valid, bad user input, typos, wrong data types'
            '401':
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/ProblemDetails'
                description: returned when the is not authenticated
            '403':
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/ProblemDetails'
                description: 'when accessing this endpoint with unsupported http verb, e.g. GET or POST'
            '404':
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/ProblemDetails'
                description: |-
                    base project not found because provided baseProjectId is non-existing 
                    the url path was not correct
            '500':
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/ProblemDetails'
                description: 'database error, any server-related error '
        summary: edit a base project
  /api/v1/BaseProjects/List:
        summary: Base Project listing
        description: Get all Base Projects
        get:
            responses:
                '200':
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/BaseProjectList'
                            examples:
                                BaseProjectListExample:
                                    value:
                                        Id: 5
                                        Name: some text
                                        Type: 67
                                        SubType: 29
                                        CountryId: 49
                                        ProductGroupIds:
                                            - 69
                                            - 19
                                        PredecessorIds:
                                            - 35
                                            - 59
                                        PeriodcityId: 69
                                        LastUpdatedOn: '2018-02-10T09:30Z'
                                        LastUpdatedBy: some text
                    description: Base Projects list fetched successfully
                '401':
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ProblemDetails'
                            examples:
                                UnAuthorized:
                                    value:
                                        type: some text
                                        title: some text
                                        status: 76
                                        detail: some text
                                        instance: some text
                                        additionalProp1: some text
                                        additionalProp2: some text
                                        additionalProp3: some text
                    description: When the user is not authenticated
                '404':
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ProblemDetails'
                            examples:
                                Problem Details Example:
                                    value:
                                        type: some text
                                        title: some text
                                        status: 15
                                        detail: some text
                                        instance: some text
                                        additionalProp1: some text
                                        additionalProp2: some text
                                        additionalProp3: some text
                    description: the url path was not correct
                '500':
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ProblemDetails'
                            examples:
                                ErrorDetails:
                                    value:
                                        type: some text
                                        title: some text
                                        status: 44
                                        detail: some text
                                        instance: some text
                                        additionalProp1: some text
                                        additionalProp2: some text
                                        additionalProp3: some text
                    description: Server error
            summary: Get All BaseProjects
            description: 'Show all Base projects '
  '/api/v1/BaseProjects/BaseProject/{id}':
        summary: Fetch Base Project by Id
        description: Fetch Base Project by Id
        get:
          parameters:
          - name: id
            in: path
            description: ''
            required: true
            style: simple
            schema:
              minimum: 1
              type: integer
              format: int64
          responses:
                '200':
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/BaseProjectEditResponse'
                           
                    description: Base project retrieced successfully
                '400':
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ProblemDetails'
                            examples:
                                ErrorDetails:
                                    value:
                                        type: some text
                                        title: some text
                                        status: 49
                                        detail: some text
                                        instance: some text
                                        additionalProp1: some text
                                        additionalProp2: some text
                                        additionalProp3: some text
                    description: 'Client validation error, Invalid Base ProjectId'
                '401':
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ProblemDetails'
                            examples:
                                ErrorDetails:
                                    value:
                                        type: some text
                                        title: some text
                                        status: 28
                                        detail: some text
                                        instance: some text
                                        additionalProp1: some text
                                        additionalProp2: some text
                                        additionalProp3: some text
                    description: User is unauthorized to view or edit BaseProject
                '404':
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ProblemDetails'
                            examples:
                                ErrorDetails:
                                    value:
                                        type: some text
                                        title: some text
                                        status: 91
                                        detail: some text
                                        instance: some text
                                        additionalProp1: some text
                                        additionalProp2: some text
                                        additionalProp3: some text
                    description: Invalid route. Base Project not found
                '500':
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ProblemDetails'
                            examples:
                                ErrorDetails:
                                    value:
                                        type: some text
                                        title: some text
                                        status: 36
                                        detail: some text
                                        instance: some text
                                        additionalProp1: some text
                                        additionalProp2: some text
                                        additionalProp3: some text
                    description: Database Error. Server error
          
  
     
       
            

 