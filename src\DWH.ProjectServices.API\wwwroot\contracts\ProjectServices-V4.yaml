{"openapi": "3.0.1", "info": {"title": "Project Services", "version": "v1"}, "paths": {"/api/v1/BaseProjects": {"post": {"tags": ["BaseProjects"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseProjectDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BaseProjectDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BaseProjectDto"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "200": {"description": "Success"}}}, "put": {"tags": ["BaseProjects"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseProjectPostDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BaseProjectPostDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BaseProjectPostDto"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Server Error"}, "200": {"description": "Success"}, "404": {"description": "Not Found"}}}, "delete": {"tags": ["BaseProjects"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseProjectDeleteDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BaseProjectDeleteDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BaseProjectDeleteDto"}}}}, "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Server Error"}, "200": {"description": "Success"}, "404": {"description": "Not Found"}}}}, "/api/v1/BaseProjects/{countryId}": {"get": {"tags": ["BaseProjects"], "parameters": [{"name": "countryId", "in": "path", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int32"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Server Error"}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BaseProjectPredecessorDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BaseProjectPredecessorDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BaseProjectPredecessorDto"}}}}}, "404": {"description": "Not Found"}}}}, "/api/v1/BaseProjects/BaseProject/{baseProjectId}": {"get": {"tags": ["BaseProjects"], "parameters": [{"name": "baseProjectId", "in": "path", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int32"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Server Error"}, "200": {"description": "Success"}, "404": {"description": "Not Found"}}}}, "/api/v1/BaseProjects/List": {"get": {"tags": ["BaseProjects"], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Server Error"}, "200": {"description": "Success"}, "404": {"description": "Not Found"}}}}, "/api/v1/BaseProjects/Name": {"get": {"tags": ["BaseProjects"], "parameters": [{"name": "countryId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "productGroupId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}, {"name": "periodicityId", "in": "query", "style": "form", "schema": {"type": "integer", "format": "int32"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Server Error"}, "200": {"description": "Success"}, "404": {"description": "Not Found"}}}}, "/api/v1/ProductGroups/{countryId}": {"get": {"tags": ["ProductGroups"], "parameters": [{"name": "countryId", "in": "path", "required": true, "style": "simple", "schema": {"type": "integer", "format": "int32"}}], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Server Error"}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductGroupDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductGroupDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProductGroupDto"}}}}}, "404": {"description": "Not Found"}}}}, "/api/v1/ProjectSubTypes": {"get": {"tags": ["ProjectSubTypes"], "responses": {"400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "Unauthorized"}, "500": {"description": "Server Error"}, "200": {"description": "Success", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectSubTypeDto"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectSubTypeDto"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProjectSubTypeDto"}}}}}, "204": {"description": "No Content"}}}}}, "components": {"schemas": {"BaseProjectDeleteDto": {"type": "object", "properties": {"id": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "BaseProjectDto": {"required": ["countryId", "isQcEnabled", "isRelevantForReporting", "modeId", "name", "periodicityId", "productGroupIds", "typeId"], "type": "object", "properties": {"name": {"maxLength": 40, "minLength": 1, "type": "string"}, "typeId": {"type": "integer", "format": "int32"}, "modeId": {"type": "integer", "format": "int32"}, "isRelevantForReporting": {"type": "boolean"}, "periodicityId": {"type": "integer", "format": "int32"}, "countryId": {"type": "integer", "format": "int32"}, "isQcEnabled": {"type": "boolean"}, "predecessorIds": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "productGroupIds": {"type": "array", "items": {"type": "integer", "format": "int32"}}}, "additionalProperties": false}, "BaseProjectPostDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"maxLength": 40, "type": "string", "nullable": true}, "typeId": {"type": "integer", "format": "int32"}, "projectSubType": {"type": "integer", "format": "int32"}, "isRelevantForReporting": {"type": "boolean"}, "isAPCChecksEnabled": {"type": "boolean"}, "periodicityId": {"type": "integer", "format": "int32"}, "isQcEnabled": {"type": "boolean"}, "predecessorIds": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "productGroupIds": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "lastUpdateOn": {"type": "string", "format": "date-time"}, "lastUpdateBy": {"type": "integer", "format": "int32", "nullable": true}}, "additionalProperties": false}, "BaseProjectPredecessorDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ProblemDetails": {"type": "object", "properties": {"type": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32", "nullable": true}, "detail": {"type": "string", "nullable": true}, "instance": {"type": "string", "nullable": true}}, "additionalProperties": {}}, "ProductGroupDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ProjectSubTypeDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "http", "description": "Enter JWT Bearer token **_only_**", "scheme": "Bearer", "bearerFormat": "JWT"}}}, "security": [{"Bearer": []}]}