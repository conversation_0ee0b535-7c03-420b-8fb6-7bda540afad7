openapi: 3.0.3
info:
  title: Project Services API
  description: |-
    This is a project Services API contracts based on the OpenAPI 3.0 
  version: 1.0.0
components:
  schemas: 
     BaseProject:
       type: object
       properties:
          Id:
              type: integer
          Name:
              type: string
          TypeId:
              type: integer
          Mode:
              type: integer
          IsRelevantForReporting:
              type: boolean
          PeriodicityId:
              type: integer
          IsQcEnabled:
              type: boolean
          PredecessorIds:
              type: array
              items:
                type: integer
          ProductGroupIds:
             type: array
             items:
                type: integer
     ProductGroups:
        type: object
        properties:
           ProductGroupId:
             type: integer
          
     ProductGroup:
           type: object
           properties:
             Id:
               type: integer
             Name:
               type: string
           
     ProjectSubType: 
            type: object
            properties:
               Id:
                   type: integer
               Name:
                  type: string
     PredecessorBaseProject: 
            type: object
            properties:
              Id:
                   type: integer
              Name:
                  type: string
  
paths: 
  /BaseProjects:
    post:
      summary: Create a BaseProject
      responses:
        '201':
          description: Created
      requestBody:
        content: 
           application/json:
             schema:
               $ref: '#/components/schemas/BaseProject'
 
  /ProductGroups/{countryId}:
    parameters:
     - schema:
         type: integer
       name: countryId
       in: path
       required: true
    get:
       summary: Get ProductGroups
       responses:
        '200':
          description: Successful
          content:
            application/json:
              schema:
                type: array
                items:
                       $ref: '#/components/schemas/ProductGroup'
  /ProjectSubTypes:
     get:
       summary: Get ProductSubTypes
       responses:
        '200':
          description: Successful
          content:
            application/json:
              schema:
                type: array
                items:
                       $ref: '#/components/schemas/ProjectSubType'
  /BaseProjects/{countryId}:
      parameters:
       - schema:
          type: integer
         name: countryId
         in: path
         required: true
       
      get:
       summary: Get BaseProject Predecessors
       responses:
        '200':
          description: Successful
          content:
            application/json:
              schema:
                type: array
                items:
                       $ref: '#/components/schemas/PredecessorBaseProject'
      
     
       
            

 